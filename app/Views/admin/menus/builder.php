<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?> - CMS Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Sortable.js for drag and drop -->
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            transition: all 0.3s ease;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
        }
        .main-content {
            background: #f8f9fa;
            min-height: 100vh;
        }
        
        /* Menu Builder Styles */
        .menu-builder {
            display: flex;
            gap: 20px;
            height: calc(100vh - 200px);
        }
        
        .menu-items-panel {
            flex: 1;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow-y: auto;
        }
        
        .menu-structure-panel {
            flex: 2;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .menu-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 10px;
            cursor: move;
            transition: all 0.3s ease;
        }
        
        .menu-item:hover {
            background: #e9ecef;
            border-color: #adb5bd;
        }
        
        .menu-structure {
            min-height: 400px;
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 20px;
        }
        
        .menu-structure.drag-over {
            border-color: #007bff;
            background: #f0f8ff;
        }
        
        .menu-structure-item {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 10px;
            position: relative;
            cursor: move;
        }
        
        .menu-structure-item:hover {
            border-color: #007bff;
        }
        
        .menu-structure-item .item-controls {
            position: absolute;
            top: 10px;
            right: 10px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .menu-structure-item:hover .item-controls {
            opacity: 1;
        }
        
        .menu-structure-item .item-controls .btn {
            padding: 2px 6px;
            font-size: 12px;
            margin-left: 2px;
        }
        
        .nested-items {
            margin-left: 30px;
            margin-top: 10px;
            border-left: 2px solid #e9ecef;
            padding-left: 15px;
        }
        
        .item-form {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .empty-menu {
            text-align: center;
            color: #6c757d;
            padding: 40px;
        }
        
        .item-type-badge {
            font-size: 0.75rem;
            padding: 2px 6px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar p-0">
                <div class="p-3">
                    <h4 class="text-white mb-4">
                        <i class="fas fa-cogs me-2"></i>CMS Admin
                    </h4>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link" href="<?= base_url('admin/dashboard') ?>">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                        
                        <a class="nav-link" href="<?= base_url('admin/pages') ?>">
                            <i class="fas fa-file-alt me-2"></i>Pages
                        </a>
                        
                        <a class="nav-link active" href="<?= base_url('admin/menus') ?>">
                            <i class="fas fa-bars me-2"></i>Menus
                        </a>
                        
                        <a class="nav-link" href="<?= base_url('admin/blog') ?>">
                            <i class="fas fa-blog me-2"></i>Blog Posts
                        </a>
                        
                        <a class="nav-link" href="<?= base_url('admin/media') ?>">
                            <i class="fas fa-images me-2"></i>Media Library
                        </a>
                        
                        <?php if ($user['role'] === 'admin'): ?>
                        <a class="nav-link" href="<?= base_url('admin/users') ?>">
                            <i class="fas fa-users me-2"></i>Users
                        </a>
                        
                        <a class="nav-link" href="<?= base_url('admin/settings') ?>">
                            <i class="fas fa-cog me-2"></i>Settings
                        </a>
                        <?php endif; ?>
                        
                        <hr class="my-3" style="border-color: rgba(255,255,255,0.3);">
                        
                        <a class="nav-link" href="<?= base_url('auth/logout') ?>">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content p-0">
                <!-- Top Navigation -->
                <nav class="navbar navbar-expand-lg navbar-light bg-white border-bottom">
                    <div class="container-fluid">
                        <h5 class="mb-0"><?= $title ?></h5>
                        
                        <div class="navbar-nav ms-auto">
                            <div class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" 
                                   data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-user-circle me-2"></i>
                                    <?= $user['first_name'] . ' ' . $user['last_name'] ?>
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>Profile</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="<?= base_url('auth/logout') ?>">
                                        <i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </nav>
                
                <!-- Page Content -->
                <div class="p-4">
                    <!-- Page Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h2><?= $title ?></h2>
                            <p class="text-muted mb-0">
                                <i class="fas fa-info-circle me-1"></i>
                                Drag and drop to organize your menu items
                            </p>
                        </div>
                        <div>
                            <a href="<?= base_url('admin/menus') ?>" class="btn btn-secondary me-2">
                                <i class="fas fa-arrow-left me-2"></i>Back to Menus
                            </a>
                            <button type="button" class="btn btn-success" onclick="saveMenuStructure()">
                                <i class="fas fa-save me-2"></i>Save Menu
                            </button>
                        </div>
                    </div>
                    
                    <!-- Menu Builder Interface -->
                    <div class="menu-builder">
                        <!-- Available Items Panel -->
                        <div class="menu-items-panel">
                            <h5 class="mb-3">
                                <i class="fas fa-plus-circle me-2"></i>Add Menu Items
                            </h5>
                            
                            <!-- Add Custom Link -->
                            <div class="item-form">
                                <h6>Custom Link</h6>
                                <form id="customLinkForm">
                                    <div class="mb-2">
                                        <input type="text" class="form-control form-control-sm" 
                                               id="customTitle" placeholder="Link Text" required>
                                    </div>
                                    <div class="mb-2">
                                        <input type="url" class="form-control form-control-sm" 
                                               id="customUrl" placeholder="https://example.com" required>
                                    </div>
                                    <button type="submit" class="btn btn-primary btn-sm">
                                        <i class="fas fa-plus me-1"></i>Add Link
                                    </button>
                                </form>
                            </div>
                            
                            <!-- Available Pages -->
                            <div class="item-form">
                                <h6>Pages</h6>
                                <div class="available-pages" style="max-height: 300px; overflow-y: auto;">
                                    <?php foreach ($availablePages as $page): ?>
                                    <div class="menu-item" data-type="page" data-page-id="<?= $page['id'] ?>" 
                                         data-title="<?= esc($page['title']) ?>" data-url="/<?= esc($page['slug']) ?>">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <strong><?= esc($page['title']) ?></strong>
                                                <br>
                                                <small class="text-muted">/<?= esc($page['slug']) ?></small>
                                            </div>
                                            <button type="button" class="btn btn-sm btn-primary" 
                                                    onclick="addPageToMenu(<?= $page['id'] ?>, '<?= esc($page['title']) ?>', '/<?= esc($page['slug']) ?>')">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                    
                                    <?php if (empty($availablePages)): ?>
                                    <div class="text-center text-muted py-3">
                                        <i class="fas fa-info-circle me-1"></i>
                                        No published pages available
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Menu Structure Panel -->
                        <div class="menu-structure-panel">
                            <h5 class="mb-3">
                                <i class="fas fa-sitemap me-2"></i>Menu Structure
                            </h5>
                            
                            <div class="menu-structure" id="menuStructure">
                                <?php if (empty($menuItems)): ?>
                                <div class="empty-menu">
                                    <i class="fas fa-bars fa-3x mb-3"></i>
                                    <h6>No menu items yet</h6>
                                    <p class="text-muted">Add pages or custom links to build your menu</p>
                                </div>
                                <?php else: ?>
                                    <?php echo renderMenuItems($menuItems); ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let menuId = <?= $menu['id'] ?>;
        let menuItemCounter = 1000; // For temporary IDs
        
        // Initialize Sortable for menu structure
        const menuStructure = document.getElementById('menuStructure');
        const sortable = Sortable.create(menuStructure, {
            group: 'menu',
            animation: 150,
            ghostClass: 'sortable-ghost',
            chosenClass: 'sortable-chosen',
            dragClass: 'sortable-drag',
            onEnd: function(evt) {
                updateMenuOrder();
            }
        });

        // Add custom link to menu
        document.getElementById('customLinkForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const title = document.getElementById('customTitle').value;
            const url = document.getElementById('customUrl').value;
            
            if (title && url) {
                addMenuItemToStructure({
                    id: 'temp_' + (++menuItemCounter),
                    title: title,
                    url: url,
                    type: 'custom',
                    page_id: null,
                    status: 'active'
                });
                
                // Clear form
                document.getElementById('customTitle').value = '';
                document.getElementById('customUrl').value = '';
            }
        });

        // Add page to menu
        function addPageToMenu(pageId, title, url) {
            addMenuItemToStructure({
                id: 'temp_' + (++menuItemCounter),
                title: title,
                url: url,
                type: 'page',
                page_id: pageId,
                status: 'active'
            });
        }

        // Add menu item to structure
        function addMenuItemToStructure(item) {
            const menuStructure = document.getElementById('menuStructure');
            
            // Remove empty state if present
            const emptyMenu = menuStructure.querySelector('.empty-menu');
            if (emptyMenu) {
                emptyMenu.remove();
            }
            
            const itemElement = createMenuItemElement(item);
            menuStructure.appendChild(itemElement);
        }

        // Create menu item element
        function createMenuItemElement(item) {
            const div = document.createElement('div');
            div.className = 'menu-structure-item';
            div.dataset.itemId = item.id;
            div.dataset.type = item.type;
            div.dataset.pageId = item.page_id || '';
            
            const typeClass = item.type === 'page' ? 'bg-primary' : 'bg-secondary';
            
            div.innerHTML = `
                <div class="item-controls">
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="editMenuItem('${item.id}')" title="Edit">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeMenuItem('${item.id}')" title="Remove">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <strong>${item.title}</strong>
                        <br>
                        <small class="text-muted">${item.url}</small>
                        <br>
                        <span class="badge ${typeClass} item-type-badge">${item.type}</span>
                    </div>
                    <div class="text-muted">
                        <i class="fas fa-grip-vertical"></i>
                    </div>
                </div>
            `;
            
            return div;
        }

        // Remove menu item
        function removeMenuItem(itemId) {
            if (confirm('Are you sure you want to remove this menu item?')) {
                const item = document.querySelector(`[data-item-id="${itemId}"]`);
                if (item) {
                    item.remove();
                    
                    // Show empty state if no items left
                    const menuStructure = document.getElementById('menuStructure');
                    if (menuStructure.children.length === 0) {
                        menuStructure.innerHTML = `
                            <div class="empty-menu">
                                <i class="fas fa-bars fa-3x mb-3"></i>
                                <h6>No menu items yet</h6>
                                <p class="text-muted">Add pages or custom links to build your menu</p>
                            </div>
                        `;
                    }
                }
            }
        }

        // Edit menu item (placeholder)
        function editMenuItem(itemId) {
            alert('Edit functionality will be implemented in the next iteration');
        }

        // Update menu order
        function updateMenuOrder() {
            // This would send the new order to the server
            console.log('Menu order updated');
        }

        // Save menu structure
        function saveMenuStructure() {
            const items = [];
            const menuItems = document.querySelectorAll('.menu-structure-item');
            
            menuItems.forEach((item, index) => {
                items.push({
                    id: item.dataset.itemId,
                    title: item.querySelector('strong').textContent,
                    url: item.querySelector('small').textContent,
                    type: item.dataset.type,
                    page_id: item.dataset.pageId || null,
                    sort_order: index,
                    parent_id: null, // For now, no nesting
                    status: 'active'
                });
            });
            
            // Send to server
            fetch(`<?= base_url('admin/menu-items/update-order') ?>`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify(items)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Menu saved successfully!');
                } else {
                    alert('Failed to save menu: ' + (data.message || 'Unknown error'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while saving the menu');
            });
        }
    </script>
</body>
</html>

<?php
// Helper function to render menu items
function renderMenuItems($items, $level = 0) {
    $html = '';
    foreach ($items as $item) {
        $typeClass = $item['type'] === 'page' ? 'bg-primary' : 'bg-secondary';
        $indent = $level > 0 ? 'style="margin-left: ' . ($level * 30) . 'px;"' : '';
        
        $html .= '<div class="menu-structure-item" data-item-id="' . $item['id'] . '" data-type="' . $item['type'] . '" data-page-id="' . ($item['page_id'] ?? '') . '" ' . $indent . '>';
        $html .= '<div class="item-controls">';
        $html .= '<button type="button" class="btn btn-sm btn-outline-primary" onclick="editMenuItem(\'' . $item['id'] . '\')" title="Edit"><i class="fas fa-edit"></i></button>';
        $html .= '<button type="button" class="btn btn-sm btn-outline-danger" onclick="removeMenuItem(\'' . $item['id'] . '\')" title="Remove"><i class="fas fa-trash"></i></button>';
        $html .= '</div>';
        $html .= '<div class="d-flex justify-content-between align-items-start">';
        $html .= '<div>';
        $html .= '<strong>' . esc($item['title']) . '</strong><br>';
        $html .= '<small class="text-muted">' . esc($item['url']) . '</small><br>';
        $html .= '<span class="badge ' . $typeClass . ' item-type-badge">' . $item['type'] . '</span>';
        $html .= '</div>';
        $html .= '<div class="text-muted"><i class="fas fa-grip-vertical"></i></div>';
        $html .= '</div>';
        $html .= '</div>';
        
        // Render children if any
        if (!empty($item['children'])) {
            $html .= renderMenuItems($item['children'], $level + 1);
        }
    }
    return $html;
}
?>
