<?= $this->extend('admin/layouts/main') ?>

<?= $this->section('title') ?>Create Template<?= $this->endSection() ?>

<?= $this->section('breadcrumb') ?>
<li class="breadcrumb-item"><a href="<?= base_url('admin/themes') ?>">Themes</a></li>
<li class="breadcrumb-item active">Create Template</li>
<?= $this->endSection() ?>

<?= $this->section('head') ?>
<link href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/theme/monokai.min.css" rel="stylesheet">
<style>
    .CodeMirror {
        border: 1px solid #ddd;
        border-radius: 4px;
        height: 400px;
    }
    .template-preview {
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 15px;
        background: #f8f9fa;
        min-height: 200px;
    }
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-plus me-2"></i>Create New Template
                </h5>
            </div>
            <div class="card-body">
                <form action="<?= base_url('admin/themes/store') ?>" method="post" enctype="multipart/form-data">
                    <?= csrf_field() ?>
                    
                    <div class="row">
                        <div class="col-md-8">
                            <!-- Basic Information -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0">Template Information</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="name" class="form-label">Template Name *</label>
                                            <input type="text" class="form-control" id="name" name="name" 
                                                   value="<?= old('name') ?>" required>
                                            <div class="form-text">Enter a descriptive name for your template</div>
                                        </div>
                                        
                                        <div class="col-md-6 mb-3">
                                            <label for="type" class="form-label">Template Type *</label>
                                            <select class="form-select" id="type" name="type" required>
                                                <option value="">Select Type</option>
                                                <option value="page" <?= old('type') === 'page' ? 'selected' : '' ?>>Page Template</option>
                                                <option value="blog" <?= old('type') === 'blog' ? 'selected' : '' ?>>Blog Template</option>
                                                <option value="layout" <?= old('type') === 'layout' ? 'selected' : '' ?>>Layout Template</option>
                                            </select>
                                            <div class="form-text">Choose the type of template you're creating</div>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="file_path" class="form-label">File Path *</label>
                                        <input type="text" class="form-control" id="file_path" name="file_path" 
                                               value="<?= old('file_path') ?>" placeholder="templates/custom/my-template.php" required>
                                        <div class="form-text">Path relative to app/Views/ directory (e.g., templates/custom/my-template.php)</div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="description" class="form-label">Description</label>
                                        <textarea class="form-control" id="description" name="description" rows="3"><?= old('description') ?></textarea>
                                        <div class="form-text">Optional description of what this template is for</div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Template Content -->
                            <div class="card mb-4">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">Template Content</h6>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button type="button" class="btn btn-outline-secondary" onclick="loadTemplate('basic')">
                                            Basic Template
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary" onclick="loadTemplate('blog')">
                                            Blog Template
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary" onclick="loadTemplate('page')">
                                            Page Template
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <textarea id="template_content" name="template_content"><?= old('template_content') ?></textarea>
                                    <div class="form-text mt-2">
                                        Write your template code here. You can use CodeIgniter view syntax and PHP.
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <!-- Preview Image -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0">Preview Image</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="preview_image" class="form-label">Upload Preview</label>
                                        <input type="file" class="form-control" id="preview_image" name="preview_image" 
                                               accept="image/*" onchange="previewImage(this)">
                                        <div class="form-text">Upload a screenshot or preview of your template</div>
                                    </div>
                                    
                                    <div id="image_preview" class="template-preview text-center" style="display: none;">
                                        <img id="preview_img" src="" alt="Preview" class="img-fluid rounded">
                                    </div>
                                    
                                    <div id="no_preview" class="template-preview text-center text-muted">
                                        <i class="fas fa-image fa-3x mb-2"></i>
                                        <p>No preview image selected</p>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Template Variables -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0">Available Variables</h6>
                                </div>
                                <div class="card-body">
                                    <small class="text-muted">
                                        <strong>Common variables you can use:</strong><br>
                                        <code>$title</code> - Page title<br>
                                        <code>$content</code> - Main content<br>
                                        <code>$meta_description</code> - Meta description<br>
                                        <code>$meta_keywords</code> - Meta keywords<br>
                                        <code>$user</code> - Current user data<br>
                                        <code>$settings</code> - Site settings<br>
                                        <br>
                                        <strong>Blog specific:</strong><br>
                                        <code>$post</code> - Blog post data<br>
                                        <code>$category</code> - Post category<br>
                                        <code>$tags</code> - Post tags<br>
                                        <code>$author</code> - Post author<br>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <a href="<?= base_url('admin/themes') ?>" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>Back to Themes
                                </a>
                                
                                <div>
                                    <button type="button" class="btn btn-outline-primary me-2" onclick="previewTemplate()">
                                        <i class="fas fa-eye me-2"></i>Preview
                                    </button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Create Template
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Template Preview</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <iframe id="preview_frame" src="" style="width: 100%; height: 500px; border: 1px solid #ddd;"></iframe>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/xml/xml.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/javascript/javascript.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/css/css.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/htmlmixed/htmlmixed.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/php/php.min.js"></script>

<script>
    // Initialize CodeMirror
    const editor = CodeMirror.fromTextArea(document.getElementById('template_content'), {
        lineNumbers: true,
        mode: 'application/x-httpd-php',
        theme: 'monokai',
        indentUnit: 4,
        lineWrapping: true,
        autoCloseTags: true,
        autoCloseBrackets: true
    });

    // Auto-generate file path from name
    document.getElementById('name').addEventListener('input', function() {
        const name = this.value;
        const type = document.getElementById('type').value;
        if (name && type) {
            const slug = name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-+|-+$/g, '');
            document.getElementById('file_path').value = `templates/${type}/${slug}.php`;
        }
    });

    document.getElementById('type').addEventListener('change', function() {
        const name = document.getElementById('name').value;
        const type = this.value;
        if (name && type) {
            const slug = name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-+|-+$/g, '');
            document.getElementById('file_path').value = `templates/${type}/${slug}.php`;
        }
    });

    // Preview image function
    function previewImage(input) {
        if (input.files && input.files[0]) {
            const reader = new FileReader();
            reader.onload = function(e) {
                document.getElementById('preview_img').src = e.target.result;
                document.getElementById('image_preview').style.display = 'block';
                document.getElementById('no_preview').style.display = 'none';
            };
            reader.readAsDataURL(input.files[0]);
        }
    }

    // Load template starters
    function loadTemplate(type) {
        let content = '';
        
        switch(type) {
            case 'basic':
                content = `<?php echo $this->extend('layouts/main') ?>

<?php echo $this->section('title') ?><?php echo $title ?><?php echo $this->endSection() ?>

<?php echo $this->section('content') ?>
<div class="container">
    <h1><?php echo esc($title) ?></h1>
    <div class="content">
        <?php echo $content ?>
    </div>
</div>
<?php echo $this->endSection() ?>`;
                break;

            case 'blog':
                content = `<?php echo $this->extend('layouts/main') ?>

<?php echo $this->section('title') ?><?php echo esc($post['title']) ?><?php echo $this->endSection() ?>

<?php echo $this->section('content') ?>
<article class="blog-post">
    <header class="post-header">
        <h1><?php echo esc($post['title']) ?></h1>
        <div class="post-meta">
            <span class="author">By <?php echo esc($author['first_name'] . ' ' . $author['last_name']) ?></span>
            <span class="date"><?php echo date('F j, Y', strtotime($post['published_at'])) ?></span>
            <?php if (!empty($category)): ?>
            <span class="category">in <?php echo esc($category['name']) ?></span>
            <?php endif; ?>
        </div>
    </header>

    <div class="post-content">
        <?php echo $post['content'] ?>
    </div>

    <?php if (!empty($tags)): ?>
    <div class="post-tags">
        <strong>Tags:</strong>
        <?php foreach ($tags as $tag): ?>
            <span class="tag"><?php echo esc($tag['name']) ?></span>
        <?php endforeach; ?>
    </div>
    <?php endif; ?>
</article>
<?php echo $this->endSection() ?>`;
                break;

            case 'page':
                content = `<?php echo $this->extend('layouts/main') ?>

<?php echo $this->section('title') ?><?php echo esc($page['title']) ?><?php echo $this->endSection() ?>

<?php echo $this->section('meta') ?>
<meta name="description" content="<?php echo esc($page['meta_description']) ?>">
<meta name="keywords" content="<?php echo esc($page['meta_keywords']) ?>">
<?php echo $this->endSection() ?>

<?php echo $this->section('content') ?>
<div class="page-content">
    <header class="page-header">
        <h1><?php echo esc($page['title']) ?></h1>
    </header>

    <div class="page-body">
        <?php echo $page['content'] ?>
    </div>
</div>
<?php echo $this->endSection() ?>`;
                break;
        }
        
        editor.setValue(content);
    }

    // Preview template function
    function previewTemplate() {
        // This would typically send the template content to a preview endpoint
        alert('Template preview functionality would be implemented here');
    }
</script>
<?= $this->endSection() ?>
