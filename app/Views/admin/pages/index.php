<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?> - CMS Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            transition: all 0.3s ease;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
        }
        .main-content {
            background: #f8f9fa;
            min-height: 100vh;
        }
        .status-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
        .action-buttons .btn {
            padding: 0.25rem 0.5rem;
            margin: 0 0.125rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar p-0">
                <div class="p-3">
                    <h4 class="text-white mb-4">
                        <i class="fas fa-cogs me-2"></i>CMS Admin
                    </h4>
                    
                    <nav class="nav flex-column">
                        <a class="nav-link" href="<?= base_url('admin/dashboard') ?>">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                        
                        <a class="nav-link active" href="<?= base_url('admin/pages') ?>">
                            <i class="fas fa-file-alt me-2"></i>Pages
                        </a>
                        
                        <a class="nav-link" href="<?= base_url('admin/blog') ?>">
                            <i class="fas fa-blog me-2"></i>Blog Posts
                        </a>
                        
                        <a class="nav-link" href="<?= base_url('admin/media') ?>">
                            <i class="fas fa-images me-2"></i>Media Library
                        </a>
                        
                        <?php if ($user['role'] === 'admin'): ?>
                        <a class="nav-link" href="<?= base_url('admin/users') ?>">
                            <i class="fas fa-users me-2"></i>Users
                        </a>
                        
                        <a class="nav-link" href="<?= base_url('admin/settings') ?>">
                            <i class="fas fa-cog me-2"></i>Settings
                        </a>
                        <?php endif; ?>
                        
                        <hr class="my-3" style="border-color: rgba(255,255,255,0.3);">
                        
                        <a class="nav-link" href="<?= base_url('auth/logout') ?>">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content p-0">
                <!-- Top Navigation -->
                <nav class="navbar navbar-expand-lg navbar-light bg-white border-bottom">
                    <div class="container-fluid">
                        <h5 class="mb-0"><?= $title ?></h5>
                        
                        <div class="navbar-nav ms-auto">
                            <div class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" 
                                   data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-user-circle me-2"></i>
                                    <?= $user['first_name'] . ' ' . $user['last_name'] ?>
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>Profile</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="<?= base_url('auth/logout') ?>">
                                        <i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </nav>
                
                <!-- Page Content -->
                <div class="p-4">
                    <?php if (session()->getFlashdata('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show">
                            <i class="fas fa-check-circle me-2"></i>
                            <?= session()->getFlashdata('success') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (session()->getFlashdata('error')): ?>
                        <div class="alert alert-danger alert-dismissible fade show">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?= session()->getFlashdata('error') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Page Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2>Pages</h2>
                        <a href="<?= base_url('admin/pages/create') ?>" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Add New Page
                        </a>
                    </div>
                    
                    <!-- Pages Table -->
                    <div class="card">
                        <div class="card-body">
                            <div class="table-responsive">
                                <table id="pagesTable" class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>Title</th>
                                            <th>Author</th>
                                            <th>Status</th>
                                            <th>Created</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($pages as $page): ?>
                                        <tr>
                                            <td>
                                                <strong><?= esc($page['title']) ?></strong>
                                                <br>
                                                <small class="text-muted">
                                                    <i class="fas fa-link me-1"></i>
                                                    <?= esc($page['slug']) ?>
                                                </small>
                                            </td>
                                            <td>
                                                <?= esc($page['first_name'] . ' ' . $page['last_name']) ?>
                                                <br>
                                                <small class="text-muted">@<?= esc($page['username']) ?></small>
                                            </td>
                                            <td>
                                                <?php
                                                $statusClass = match($page['status']) {
                                                    'published' => 'bg-success',
                                                    'draft' => 'bg-warning',
                                                    'private' => 'bg-info',
                                                    default => 'bg-secondary'
                                                };
                                                ?>
                                                <span class="badge <?= $statusClass ?> status-badge">
                                                    <?= ucfirst($page['status']) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?= date('M j, Y', strtotime($page['created_at'])) ?>
                                                <br>
                                                <small class="text-muted">
                                                    <?= date('g:i A', strtotime($page['created_at'])) ?>
                                                </small>
                                            </td>
                                            <td>
                                                <div class="action-buttons">
                                                    <a href="<?= base_url('admin/pages/edit/' . $page['id']) ?>" 
                                                       class="btn btn-sm btn-outline-primary" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="<?= base_url('admin/pages/preview/' . $page['id']) ?>" 
                                                       class="btn btn-sm btn-outline-info" title="Preview" target="_blank">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                                            onclick="deletePage(<?= $page['id'] ?>)" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    
    <script>
        $(document).ready(function() {
            $('#pagesTable').DataTable({
                responsive: true,
                order: [[3, 'desc']],
                pageLength: 25,
                language: {
                    search: "Search pages:",
                    lengthMenu: "Show _MENU_ pages per page",
                    info: "Showing _START_ to _END_ of _TOTAL_ pages",
                    paginate: {
                        first: "First",
                        last: "Last",
                        next: "Next",
                        previous: "Previous"
                    }
                }
            });
        });

        function deletePage(id) {
            if (confirm('Are you sure you want to delete this page? This action cannot be undone.')) {
                fetch(`<?= base_url('admin/pages/delete/') ?>${id}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert(data.message || 'Failed to delete page');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while deleting the page');
                });
            }
        }
    </script>
</body>
</html>
