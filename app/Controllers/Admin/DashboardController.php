<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\UserModel;
use App\Models\PageModel;
use App\Models\BlogPostModel;
use App\Models\MediaModel;

class DashboardController extends BaseController
{
    protected $userModel;
    protected $pageModel;
    protected $blogPostModel;
    protected $mediaModel;
    protected $session;

    public function __construct()
    {
        $this->userModel = new UserModel();
        $this->pageModel = new PageModel();
        $this->blogPostModel = new BlogPostModel();
        $this->mediaModel = new MediaModel();
        $this->session = session();
    }

    public function index()
    {
        // Get statistics
        $stats = [
            'total_pages' => $this->pageModel->countAllResults(),
            'total_posts' => $this->blogPostModel->countAllResults(),
            'total_users' => $this->userModel->countAllResults(),
            'total_media' => $this->mediaModel->countAllResults(),
        ];

        // Get recent pages (limit 5)
        $recent_pages = $this->pageModel->orderBy('created_at', 'DESC')->limit(5)->findAll();

        // Get recent blog posts (limit 5)
        $recent_posts = $this->blogPostModel->orderBy('created_at', 'DESC')->limit(5)->findAll();

        $data = [
            'title' => 'Dashboard',
            'user' => [
                'id' => $this->session->get('user_id'),
                'username' => $this->session->get('username'),
                'email' => $this->session->get('email'),
                'role' => $this->session->get('role'),
                'first_name' => $this->session->get('first_name'),
                'last_name' => $this->session->get('last_name'),
            ],
            'stats' => $stats,
            'recent_pages' => $recent_pages,
            'recent_posts' => $recent_posts
        ];

        return view('admin/dashboard', $data);
    }
}
