{"url": "http://localhost:8081/admin/menus", "method": "GET", "isAJAX": false, "startTime": **********.271005, "totalTime": 72.8, "totalMemory": "7.225", "segmentDuration": 15, "segmentCount": 5, "CI_VERSION": "4.6.1", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.275077, "duration": 0.021754980087280273}, {"name": "Required Before Filters", "component": "Timer", "start": **********.296834, "duration": 0.003097057342529297}, {"name": "Routing", "component": "Timer", "start": **********.299938, "duration": 0.0016040802001953125}, {"name": "Before Filters", "component": "Timer", "start": **********.301886, "duration": 0.006567955017089844}, {"name": "Controller", "component": "Timer", "start": **********.30846, "duration": 0.**************}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.308461, "duration": 0.013328075408935547}, {"name": "After Filters", "component": "Timer", "start": **********.343326, "duration": 1.0967254638671875e-05}, {"name": "Required After Filters", "component": "Timer", "start": **********.343381, "duration": 0.0005011558532714844}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(1 total Query, 1  unique across 1 Connection)", "display": {"queries": [{"hover": "", "class": "", "duration": "6.9 ms", "sql": "<strong>SELECT</strong> `menus`.*, `users`.`username`, `users`.`first_name`, `users`.`last_name`\n<strong>FROM</strong> `menus`\n<strong>JOIN</strong> `users` <strong>ON</strong> `users`.`id` = `menus`.`created_by`\n<strong>ORDER</strong> <strong>BY</strong> `menus`.`created_at` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/MenuModel.php:138", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Admin/MenuController.php:32", "function": "        App\\Models\\MenuModel->getMenusWithCreator()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Admin\\MenuController->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}, {"file": "SYSTEMPATH/rewrite.php:44", "args": ["/opt/lampp/htdocs/cms/public/index.php"], "function": "        require_once()", "index": " 12    "}], "trace-file": "APPPATH/Models/MenuModel.php:138", "qid": "926416aa6f624555a642b7ab5c3461ec"}]}, "badgeValue": 1, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": **********.328658, "duration": "0.001392"}, {"name": "Query", "component": "Database", "start": **********.33134, "duration": "0.006896", "query": "<strong>SELECT</strong> `menus`.*, `users`.`username`, `users`.`first_name`, `users`.`last_name`\n<strong>FROM</strong> `menus`\n<strong>JOIN</strong> `users` <strong>ON</strong> `users`.`id` = `menus`.`created_by`\n<strong>ORDER</strong> <strong>BY</strong> `menus`.`created_at` <strong>DESC</strong>"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": [{"level": "debug", "msg": "Session: Class initialized using 'CodeIgniter\\Session\\Handlers\\FileHandler' driver."}]}, "badgeValue": null, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": [], "badgeValue": 1, "isEmpty": false, "hasTabContent": false, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "View: admin/menus/index.php", "component": "Views", "start": **********.341116, "duration": 0.0021550655364990234}]}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 164 )", "display": {"coreFiles": [{"path": "SYSTEMPATH/API/ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH/Autoloader/Autoloader.php", "name": "Autoloader.php"}, {"path": "SYSTEMPATH/Autoloader/FileLocator.php", "name": "FileLocator.php"}, {"path": "SYSTEMPATH/Autoloader/FileLocatorInterface.php", "name": "FileLocatorInterface.php"}, {"path": "SYSTEMPATH/BaseModel.php", "name": "BaseModel.php"}, {"path": "SYSTEMPATH/Boot.php", "name": "Boot.php"}, {"path": "SYSTEMPATH/Cache/CacheFactory.php", "name": "CacheFactory.php"}, {"path": "SYSTEMPATH/Cache/CacheInterface.php", "name": "CacheInterface.php"}, {"path": "SYSTEMPATH/Cache/Handlers/BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH/Cache/Handlers/FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH/Cache/ResponseCache.php", "name": "ResponseCache.php"}, {"path": "SYSTEMPATH/CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "SYSTEMPATH/Common.php", "name": "Common.php"}, {"path": "SYSTEMPATH/Config/AutoloadConfig.php", "name": "AutoloadConfig.php"}, {"path": "SYSTEMPATH/Config/BaseConfig.php", "name": "BaseConfig.php"}, {"path": "SYSTEMPATH/Config/BaseService.php", "name": "BaseService.php"}, {"path": "SYSTEMPATH/Config/DotEnv.php", "name": "DotEnv.php"}, {"path": "SYSTEMPATH/Config/Factories.php", "name": "Factories.php"}, {"path": "SYSTEMPATH/Config/Factory.php", "name": "Factory.php"}, {"path": "SYSTEMPATH/Config/Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH/Config/Routing.php", "name": "Routing.php"}, {"path": "SYSTEMPATH/Config/Services.php", "name": "Services.php"}, {"path": "SYSTEMPATH/Config/View.php", "name": "View.php"}, {"path": "SYSTEMPATH/Controller.php", "name": "Controller.php"}, {"path": "SYSTEMPATH/Cookie/CloneableCookieInterface.php", "name": "CloneableCookieInterface.php"}, {"path": "SYSTEMPATH/Cookie/Cookie.php", "name": "Cookie.php"}, {"path": "SYSTEMPATH/Cookie/CookieInterface.php", "name": "CookieInterface.php"}, {"path": "SYSTEMPATH/Cookie/CookieStore.php", "name": "CookieStore.php"}, {"path": "SYSTEMPATH/Database/BaseBuilder.php", "name": "BaseBuilder.php"}, {"path": "SYSTEMPATH/Database/BaseConnection.php", "name": "BaseConnection.php"}, {"path": "SYSTEMPATH/Database/BaseResult.php", "name": "BaseResult.php"}, {"path": "SYSTEMPATH/Database/Config.php", "name": "Config.php"}, {"path": "SYSTEMPATH/Database/ConnectionInterface.php", "name": "ConnectionInterface.php"}, {"path": "SYSTEMPATH/Database/Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH/Database/MySQLi/Builder.php", "name": "Builder.php"}, {"path": "SYSTEMPATH/Database/MySQLi/Connection.php", "name": "Connection.php"}, {"path": "SYSTEMPATH/Database/MySQLi/Result.php", "name": "Result.php"}, {"path": "SYSTEMPATH/Database/Query.php", "name": "Query.php"}, {"path": "SYSTEMPATH/Database/QueryInterface.php", "name": "QueryInterface.php"}, {"path": "SYSTEMPATH/Database/ResultInterface.php", "name": "ResultInterface.php"}, {"path": "SYSTEMPATH/Debug/Exceptions.php", "name": "Exceptions.php"}, {"path": "SYSTEMPATH/Debug/Timer.php", "name": "Timer.php"}, {"path": "SYSTEMPATH/Debug/Toolbar.php", "name": "Toolbar.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/BaseCollector.php", "name": "BaseCollector.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Files.php", "name": "Files.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Logs.php", "name": "Logs.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Routes.php", "name": "Routes.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Timers.php", "name": "Timers.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Views.php", "name": "Views.php"}, {"path": "SYSTEMPATH/Events/Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH/Filters/DebugToolbar.php", "name": "DebugToolbar.php"}, {"path": "SYSTEMPATH/Filters/FilterInterface.php", "name": "FilterInterface.php"}, {"path": "SYSTEMPATH/Filters/Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH/Filters/ForceHTTPS.php", "name": "ForceHTTPS.php"}, {"path": "SYSTEMPATH/Filters/PageCache.php", "name": "PageCache.php"}, {"path": "SYSTEMPATH/Filters/PerformanceMetrics.php", "name": "PerformanceMetrics.php"}, {"path": "SYSTEMPATH/HTTP/ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "SYSTEMPATH/HTTP/Header.php", "name": "Header.php"}, {"path": "SYSTEMPATH/HTTP/IncomingRequest.php", "name": "IncomingRequest.php"}, {"path": "SYSTEMPATH/HTTP/Message.php", "name": "Message.php"}, {"path": "SYSTEMPATH/HTTP/MessageInterface.php", "name": "MessageInterface.php"}, {"path": "SYSTEMPATH/HTTP/MessageTrait.php", "name": "MessageTrait.php"}, {"path": "SYSTEMPATH/HTTP/Method.php", "name": "Method.php"}, {"path": "SYSTEMPATH/HTTP/OutgoingRequest.php", "name": "OutgoingRequest.php"}, {"path": "SYSTEMPATH/HTTP/OutgoingRequestInterface.php", "name": "OutgoingRequestInterface.php"}, {"path": "SYSTEMPATH/HTTP/Request.php", "name": "Request.php"}, {"path": "SYSTEMPATH/HTTP/RequestInterface.php", "name": "RequestInterface.php"}, {"path": "SYSTEMPATH/HTTP/RequestTrait.php", "name": "RequestTrait.php"}, {"path": "SYSTEMPATH/HTTP/Response.php", "name": "Response.php"}, {"path": "SYSTEMPATH/HTTP/ResponseInterface.php", "name": "ResponseInterface.php"}, {"path": "SYSTEMPATH/HTTP/ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH/HTTP/SiteURI.php", "name": "SiteURI.php"}, {"path": "SYSTEMPATH/HTTP/SiteURIFactory.php", "name": "SiteURIFactory.php"}, {"path": "SYSTEMPATH/HTTP/URI.php", "name": "URI.php"}, {"path": "SYSTEMPATH/HTTP/UserAgent.php", "name": "UserAgent.php"}, {"path": "SYSTEMPATH/Helpers/array_helper.php", "name": "array_helper.php"}, {"path": "SYSTEMPATH/Helpers/filesystem_helper.php", "name": "filesystem_helper.php"}, {"path": "SYSTEMPATH/Helpers/kint_helper.php", "name": "kint_helper.php"}, {"path": "SYSTEMPATH/Helpers/url_helper.php", "name": "url_helper.php"}, {"path": "SYSTEMPATH/I18n/Time.php", "name": "Time.php"}, {"path": "SYSTEMPATH/I18n/TimeTrait.php", "name": "TimeTrait.php"}, {"path": "SYSTEMPATH/Log/Handlers/BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH/Log/Handlers/FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH/Log/Handlers/HandlerInterface.php", "name": "HandlerInterface.php"}, {"path": "SYSTEMPATH/Log/Logger.php", "name": "Logger.php"}, {"path": "SYSTEMPATH/Model.php", "name": "Model.php"}, {"path": "SYSTEMPATH/Modules/Modules.php", "name": "Modules.php"}, {"path": "SYSTEMPATH/Router/RouteCollection.php", "name": "RouteCollection.php"}, {"path": "SYSTEMPATH/Router/RouteCollectionInterface.php", "name": "RouteCollectionInterface.php"}, {"path": "SYSTEMPATH/Router/Router.php", "name": "Router.php"}, {"path": "SYSTEMPATH/Router/RouterInterface.php", "name": "RouterInterface.php"}, {"path": "SYSTEMPATH/Session/Handlers/BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH/Session/Handlers/FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH/Session/Session.php", "name": "Session.php"}, {"path": "SYSTEMPATH/Session/SessionInterface.php", "name": "SessionInterface.php"}, {"path": "SYSTEMPATH/Superglobals.php", "name": "Superglobals.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/FacadeInterface.php", "name": "FacadeInterface.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Kint.php", "name": "Kint.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/AbstractRenderer.php", "name": "AbstractRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/AssetRendererTrait.php", "name": "AssetRendererTrait.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/CliRenderer.php", "name": "CliRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/ConstructableRendererInterface.php", "name": "ConstructableRendererInterface.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/RichRenderer.php", "name": "RichRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/TextRenderer.php", "name": "TextRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Utils.php", "name": "Utils.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/init.php", "name": "init.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/init_helpers.php", "name": "init_helpers.php"}, {"path": "SYSTEMPATH/Traits/ConditionalTrait.php", "name": "ConditionalTrait.php"}, {"path": "SYSTEMPATH/Validation/FormatRules.php", "name": "FormatRules.php"}, {"path": "SYSTEMPATH/View/RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH/View/View.php", "name": "View.php"}, {"path": "SYSTEMPATH/View/ViewDecoratorTrait.php", "name": "ViewDecoratorTrait.php"}, {"path": "SYSTEMPATH/rewrite.php", "name": "rewrite.php"}], "userFiles": [{"path": "APPPATH/Common.php", "name": "Common.php"}, {"path": "APPPATH/Config/App.php", "name": "App.php"}, {"path": "APPPATH/Config/Autoload.php", "name": "Autoload.php"}, {"path": "APPPATH/Config/Boot/development.php", "name": "development.php"}, {"path": "APPPATH/Config/Cache.php", "name": "Cache.php"}, {"path": "APPPATH/Config/Constants.php", "name": "Constants.php"}, {"path": "APPPATH/Config/ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "APPPATH/Config/Cookie.php", "name": "Cookie.php"}, {"path": "APPPATH/Config/Database.php", "name": "Database.php"}, {"path": "APPPATH/Config/Events.php", "name": "Events.php"}, {"path": "APPPATH/Config/Exceptions.php", "name": "Exceptions.php"}, {"path": "APPPATH/Config/Feature.php", "name": "Feature.php"}, {"path": "APPPATH/Config/Filters.php", "name": "Filters.php"}, {"path": "APPPATH/Config/Kint.php", "name": "Kint.php"}, {"path": "APPPATH/Config/Logger.php", "name": "Logger.php"}, {"path": "APPPATH/Config/Modules.php", "name": "Modules.php"}, {"path": "APPPATH/Config/Optimize.php", "name": "Optimize.php"}, {"path": "APPPATH/Config/Paths.php", "name": "Paths.php"}, {"path": "APPPATH/Config/Routes.php", "name": "Routes.php"}, {"path": "APPPATH/Config/Routing.php", "name": "Routing.php"}, {"path": "APPPATH/Config/Services.php", "name": "Services.php"}, {"path": "APPPATH/Config/Session.php", "name": "Session.php"}, {"path": "APPPATH/Config/Toolbar.php", "name": "Toolbar.php"}, {"path": "APPPATH/Config/UserAgents.php", "name": "UserAgents.php"}, {"path": "APPPATH/Config/View.php", "name": "View.php"}, {"path": "APPPATH/Controllers/Admin/MenuController.php", "name": "MenuController.php"}, {"path": "APPPATH/Controllers/BaseController.php", "name": "BaseController.php"}, {"path": "APPPATH/Filters/AuthFilter.php", "name": "AuthFilter.php"}, {"path": "APPPATH/Models/MenuItemModel.php", "name": "MenuItemModel.php"}, {"path": "APPPATH/Models/MenuModel.php", "name": "MenuModel.php"}, {"path": "APPPATH/Models/PageModel.php", "name": "PageModel.php"}, {"path": "APPPATH/Views/admin/menus/index.php", "name": "index.php"}, {"path": "FCPATH/index.php", "name": "index.php"}, {"path": "VENDORPATH/autoload.php", "name": "autoload.php"}, {"path": "VENDORPATH/composer/ClassLoader.php", "name": "ClassLoader.php"}, {"path": "VENDORPATH/composer/InstalledVersions.php", "name": "InstalledVersions.php"}, {"path": "VENDORPATH/composer/autoload_real.php", "name": "autoload_real.php"}, {"path": "VENDORPATH/composer/autoload_static.php", "name": "autoload_static.php"}, {"path": "VENDORPATH/composer/installed.php", "name": "installed.php"}, {"path": "VENDORPATH/composer/platform_check.php", "name": "platform_check.php"}, {"path": "VENDORPATH/laminas/laminas-escaper/src/Escaper.php", "name": "Escaper.php"}, {"path": "VENDORPATH/laminas/laminas-escaper/src/EscaperInterface.php", "name": "EscaperInterface.php"}, {"path": "VENDORPATH/myclabs/deep-copy/src/DeepCopy/deep_copy.php", "name": "deep_copy.php"}, {"path": "VENDORPATH/phpunit/phpunit/src/Framework/Assert/Functions.php", "name": "Functions.php"}, {"path": "VENDORPATH/psr/log/src/LogLevel.php", "name": "LogLevel.php"}, {"path": "VENDORPATH/psr/log/src/LoggerAwareTrait.php", "name": "LoggerAwareTrait.php"}, {"path": "VENDORPATH/psr/log/src/LoggerInterface.php", "name": "LoggerInterface.php"}, {"path": "VENDORPATH/symfony/deprecation-contracts/function.php", "name": "function.php"}]}, "badgeValue": 164, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"directory": "", "controller": "\\App\\Controllers\\Admin\\MenuController", "method": "index", "paramCount": 0, "truePCount": 0, "params": []}], "routes": [{"method": "GET", "route": "/", "handler": "\\App\\Controllers\\Home::index"}, {"method": "GET", "route": "page/([^/]+)", "handler": "\\App\\Controllers\\Home::page/$1"}, {"method": "GET", "route": "auth/login", "handler": "\\App\\Controllers\\AuthController::login"}, {"method": "GET", "route": "auth/register", "handler": "\\App\\Controllers\\AuthController::register"}, {"method": "GET", "route": "auth/logout", "handler": "\\App\\Controllers\\AuthController::logout"}, {"method": "GET", "route": "auth/check", "handler": "\\App\\Controllers\\AuthController::checkAuth"}, {"method": "GET", "route": "admin/dashboard", "handler": "\\App\\Controllers\\Admin\\DashboardController::index"}, {"method": "GET", "route": "admin/pages", "handler": "\\App\\Controllers\\Admin\\PageController::index"}, {"method": "GET", "route": "admin/pages/create", "handler": "\\App\\Controllers\\Admin\\PageController::create"}, {"method": "GET", "route": "admin/pages/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\PageController::edit/$1"}, {"method": "GET", "route": "admin/pages/preview/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\PageController::preview/$1"}, {"method": "GET", "route": "admin/menus", "handler": "\\App\\Controllers\\Admin\\MenuController::index"}, {"method": "GET", "route": "admin/menus/create", "handler": "\\App\\Controllers\\Admin\\MenuController::create"}, {"method": "GET", "route": "admin/menus/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\MenuController::edit/$1"}, {"method": "GET", "route": "admin/menus/builder/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\MenuController::builder/$1"}, {"method": "GET", "route": "admin/menu-items/get/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\MenuItemController::get/$1"}, {"method": "GET", "route": "admin/menu-items/menu/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\MenuItemController::getMenuItems/$1"}, {"method": "GET", "route": "admin/menu-items/search-pages", "handler": "\\App\\Controllers\\Admin\\MenuItemController::searchPages"}, {"method": "GET", "route": "admin/blog", "handler": "\\App\\Controllers\\Admin\\BlogController::index"}, {"method": "GET", "route": "admin/blog/create", "handler": "\\App\\Controllers\\Admin\\BlogController::create"}, {"method": "GET", "route": "admin/blog/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\BlogController::edit/$1"}, {"method": "GET", "route": "admin/blog/search", "handler": "\\App\\Controllers\\Admin\\BlogController::search"}, {"method": "GET", "route": "admin/blog/by-status", "handler": "\\App\\Controllers\\Admin\\BlogController::getPostsByStatus"}, {"method": "GET", "route": "admin/categories", "handler": "\\App\\Controllers\\Admin\\CategoryController::index"}, {"method": "GET", "route": "admin/categories/create", "handler": "\\App\\Controllers\\Admin\\CategoryController::create"}, {"method": "GET", "route": "admin/categories/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\CategoryController::edit/$1"}, {"method": "GET", "route": "admin/tags", "handler": "\\App\\Controllers\\Admin\\TagController::index"}, {"method": "GET", "route": "admin/tags/create", "handler": "\\App\\Controllers\\Admin\\TagController::create"}, {"method": "GET", "route": "admin/tags/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\TagController::edit/$1"}, {"method": "GET", "route": "admin/tags/search", "handler": "\\App\\Controllers\\Admin\\TagController::search"}, {"method": "GET", "route": "admin/comments", "handler": "\\App\\Controllers\\Admin\\CommentController::index"}, {"method": "GET", "route": "admin/comments/pending", "handler": "\\App\\Controllers\\Admin\\CommentController::pending"}, {"method": "GET", "route": "admin/media", "handler": "\\App\\Controllers\\Admin\\MediaController::index"}, {"method": "GET", "route": "admin/media/get-media", "handler": "\\App\\Controllers\\Admin\\MediaController::getMedia"}, {"method": "GET", "route": "admin/media/details/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\MediaController::details/$1"}, {"method": "GET", "route": "admin/media/picker", "handler": "\\App\\Controllers\\Admin\\MediaController::picker"}, {"method": "GET", "route": "admin/media/stats", "handler": "\\App\\Controllers\\Admin\\MediaController::stats"}, {"method": "GET", "route": "admin/themes", "handler": "\\App\\Controllers\\Admin\\ThemeController::index"}, {"method": "GET", "route": "admin/themes/create", "handler": "\\App\\Controllers\\Admin\\ThemeController::create"}, {"method": "GET", "route": "admin/themes/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\ThemeController::edit/$1"}, {"method": "GET", "route": "admin/themes/settings", "handler": "\\App\\Controllers\\Admin\\ThemeController::settings"}, {"method": "POST", "route": "auth/login", "handler": "\\App\\Controllers\\AuthController::processLogin"}, {"method": "POST", "route": "auth/register", "handler": "\\App\\Controllers\\AuthController::processRegister"}, {"method": "POST", "route": "admin/pages/store", "handler": "\\App\\Controllers\\Admin\\PageController::store"}, {"method": "POST", "route": "admin/pages/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\PageController::update/$1"}, {"method": "POST", "route": "admin/menus/store", "handler": "\\App\\Controllers\\Admin\\MenuController::store"}, {"method": "POST", "route": "admin/menus/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\MenuController::update/$1"}, {"method": "POST", "route": "admin/menus/duplicate/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\MenuController::duplicate/$1"}, {"method": "POST", "route": "admin/menu-items/store", "handler": "\\App\\Controllers\\Admin\\MenuItemController::store"}, {"method": "POST", "route": "admin/menu-items/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\MenuItemController::update/$1"}, {"method": "POST", "route": "admin/menu-items/update-order", "handler": "\\App\\Controllers\\Admin\\MenuItemController::updateOrder"}, {"method": "POST", "route": "admin/menu-items/bulk-status", "handler": "\\App\\Controllers\\Admin\\MenuItemController::bulkUpdateStatus"}, {"method": "POST", "route": "admin/blog/store", "handler": "\\App\\Controllers\\Admin\\BlogController::store"}, {"method": "POST", "route": "admin/blog/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\BlogController::update/$1"}, {"method": "POST", "route": "admin/blog/duplicate/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\BlogController::duplicate/$1"}, {"method": "POST", "route": "admin/blog/bulk-action", "handler": "\\App\\Controllers\\Admin\\BlogController::bulkAction"}, {"method": "POST", "route": "admin/categories/store", "handler": "\\App\\Controllers\\Admin\\CategoryController::store"}, {"method": "POST", "route": "admin/categories/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\CategoryController::update/$1"}, {"method": "POST", "route": "admin/categories/reorder", "handler": "\\App\\Controllers\\Admin\\CategoryController::reorder"}, {"method": "POST", "route": "admin/tags/store", "handler": "\\App\\Controllers\\Admin\\TagController::store"}, {"method": "POST", "route": "admin/tags/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\TagController::update/$1"}, {"method": "POST", "route": "admin/comments/approve/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\CommentController::approve/$1"}, {"method": "POST", "route": "admin/comments/spam/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\CommentController::spam/$1"}, {"method": "POST", "route": "admin/comments/trash/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\CommentController::trash/$1"}, {"method": "POST", "route": "admin/comments/bulk-action", "handler": "\\App\\Controllers\\Admin\\CommentController::bulkAction"}, {"method": "POST", "route": "admin/media/upload", "handler": "\\App\\Controllers\\Admin\\MediaController::upload"}, {"method": "POST", "route": "admin/media/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\MediaController::update/$1"}, {"method": "POST", "route": "admin/media/bulk-delete", "handler": "\\App\\Controllers\\Admin\\MediaController::bulkDelete"}, {"method": "POST", "route": "admin/media/generate-thumbnails", "handler": "\\App\\Controllers\\Admin\\MediaController::generateThumbnails"}, {"method": "POST", "route": "admin/media/cleanup", "handler": "\\App\\Controllers\\Admin\\MediaController::cleanup"}, {"method": "POST", "route": "admin/themes/store", "handler": "\\App\\Controllers\\Admin\\ThemeController::store"}, {"method": "POST", "route": "admin/themes/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\ThemeController::update/$1"}, {"method": "POST", "route": "admin/themes/set-default/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\ThemeController::setDefault/$1"}, {"method": "POST", "route": "admin/themes/settings", "handler": "\\App\\Controllers\\Admin\\ThemeController::updateSettings"}, {"method": "DELETE", "route": "admin/pages/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\PageController::delete/$1"}, {"method": "DELETE", "route": "admin/menus/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\MenuController::delete/$1"}, {"method": "DELETE", "route": "admin/menu-items/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\MenuItemController::delete/$1"}, {"method": "DELETE", "route": "admin/blog/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\BlogController::delete/$1"}, {"method": "DELETE", "route": "admin/categories/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\CategoryController::delete/$1"}, {"method": "DELETE", "route": "admin/tags/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\TagController::delete/$1"}, {"method": "DELETE", "route": "admin/comments/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\CommentController::delete/$1"}, {"method": "DELETE", "route": "admin/media/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\MediaController::delete/$1"}, {"method": "DELETE", "route": "admin/themes/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\ThemeController::delete/$1"}]}, "badgeValue": 42, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "9.52", "count": 1}, "DBQuery": {"event": "DB<PERSON>uery", "duration": "0.06", "count": 1}}}, "badgeValue": 2, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.287303, "duration": 0.009521007537841797}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.338248, "duration": 5.602836608886719e-05}]}], "vars": {"varData": {"View Data": {"title": "Menus", "menus": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (3)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (3)</li><li>Contents (3)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>id</th><th>name</th><th>slug</th><th>description</th><th>location</th><th>status</th><th>created_by</th><th>created_at</th><th>updated_at</th><th>username</th><th>first_name</th><th>last_name</th></tr></thead><tbody><tr><th>0</th><td title=\"string (1)\">1</td><td title=\"string (18)\">Primary Navigation</td><td title=\"string (18)\">primary-navigation</td><td title=\"string (36)\">Main navigation menu for the website</td><td title=\"string (7)\">primary</td><td title=\"string (6)\">active</td><td title=\"string (1)\">1</td><td title=\"string (19)\">2025-06-20 09:44:06</td><td title=\"string (19)\">2025-06-20 09:44:06</td><td title=\"string (5)\">admin</td><td title=\"string (5)\">Admin</td><td title=\"string (4)\">User</td></tr><tr><th>1</th><td title=\"string (1)\">2</td><td title=\"string (11)\">Footer Menu</td><td title=\"string (11)\">footer-menu</td><td title=\"string (22)\">Footer navigation menu</td><td title=\"string (6)\">footer</td><td title=\"string (6)\">active</td><td title=\"string (1)\">1</td><td title=\"string (19)\">2025-06-20 09:44:06</td><td title=\"string (19)\">2025-06-20 09:44:06</td><td title=\"string (5)\">admin</td><td title=\"string (5)\">Admin</td><td title=\"string (4)\">User</td></tr><tr><th>2</th><td title=\"string (1)\">3</td><td title=\"string (12)\">Sidebar Menu</td><td title=\"string (12)\">sidebar-menu</td><td title=\"string (23)\">Sidebar navigation menu</td><td title=\"string (7)\">sidebar</td><td title=\"string (6)\">active</td><td title=\"string (1)\">1</td><td title=\"string (19)\">2025-06-20 09:44:06</td><td title=\"string (19)\">2025-06-20 09:44:06</td><td title=\"string (5)\">admin</td><td title=\"string (5)\">Admin</td><td title=\"string (4)\">User</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (12)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (18) \"Primary Navigation\"<div class=\"access-path\">$value[0]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (18) \"primary-navigation\"<div class=\"access-path\">$value[0]['slug']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>string</var> (36) \"Main navigation menu for the website\"<div class=\"access-path\">$value[0]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>location</dfn> =&gt; <var>string</var> (7) \"primary\"<div class=\"access-path\">$value[0]['location']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[0]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_by</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['created_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-20 09:44:06\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-20 09:44:06\"<div class=\"access-path\">$value[0]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>username</dfn> =&gt; <var>string</var> (5) \"admin\"<div class=\"access-path\">$value[0]['username']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>first_name</dfn> =&gt; <var>string</var> (5) \"Admin\"<div class=\"access-path\">$value[0]['first_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_name</dfn> =&gt; <var>string</var> (4) \"User\"<div class=\"access-path\">$value[0]['last_name']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (12)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[1]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (11) \"Footer Menu\"<div class=\"access-path\">$value[1]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (11) \"footer-menu\"<div class=\"access-path\">$value[1]['slug']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>string</var> (22) \"Footer navigation menu\"<div class=\"access-path\">$value[1]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>location</dfn> =&gt; <var>string</var> (6) \"footer\"<div class=\"access-path\">$value[1]['location']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[1]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_by</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[1]['created_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-20 09:44:06\"<div class=\"access-path\">$value[1]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-20 09:44:06\"<div class=\"access-path\">$value[1]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>username</dfn> =&gt; <var>string</var> (5) \"admin\"<div class=\"access-path\">$value[1]['username']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>first_name</dfn> =&gt; <var>string</var> (5) \"Admin\"<div class=\"access-path\">$value[1]['first_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_name</dfn> =&gt; <var>string</var> (4) \"User\"<div class=\"access-path\">$value[1]['last_name']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (12)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[2]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (12) \"Sidebar Menu\"<div class=\"access-path\">$value[2]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (12) \"sidebar-menu\"<div class=\"access-path\">$value[2]['slug']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>string</var> (23) \"Sidebar navigation menu\"<div class=\"access-path\">$value[2]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>location</dfn> =&gt; <var>string</var> (7) \"sidebar\"<div class=\"access-path\">$value[2]['location']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[2]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_by</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[2]['created_by']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-20 09:44:06\"<div class=\"access-path\">$value[2]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-20 09:44:06\"<div class=\"access-path\">$value[2]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>username</dfn> =&gt; <var>string</var> (5) \"admin\"<div class=\"access-path\">$value[2]['username']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>first_name</dfn> =&gt; <var>string</var> (5) \"Admin\"<div class=\"access-path\">$value[2]['first_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_name</dfn> =&gt; <var>string</var> (4) \"User\"<div class=\"access-path\">$value[2]['last_name']</div></dt></dl></dd></dl></li></ul></dd></dl></div>", "user": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (4)</dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>role</dfn> =&gt; <var>string</var> (5) \"admin\"<div class=\"access-path\">$value['role']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>first_name</dfn> =&gt; <var>string</var> (5) \"Admin\"<div class=\"access-path\">$value['first_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_name</dfn> =&gt; <var>string</var> (4) \"User\"<div class=\"access-path\">$value['last_name']</div></dt></dl></dd></dl></div>"}}, "session": {"__ci_last_regenerate": "<pre>**********</pre>", "_ci_previous_url": "http://localhost:8081/admin/menus", "user_id": "1", "username": "admin", "email": "<EMAIL>", "role": "admin", "first_name": "Admin", "last_name": "User", "is_logged_in": "<pre>1</pre>", "__ci_vars": "<pre>Array\n(\n)\n</pre>"}, "headers": {"Host": "localhost:8081", "Connection": "keep-alive", "Sec-Ch-Ua": "&quot;Brave&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;", "Sec-Ch-Ua-Mobile": "?0", "Sec-Ch-Ua-Platform": "&quot;Linux&quot;", "Upgrade-Insecure-Requests": "1", "User-Agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8", "Sec-Gpc": "1", "Accept-Language": "en-US,en;q=0.8", "Sec-Fetch-Site": "none", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-User": "?1", "Sec-Fetch-Dest": "document", "Accept-Encoding": "gzip, deflate, br, zstd", "Cookie": "admin_auth=eyJpdiI6IkQ3T3RUQngyc043Q3RZSWVnSjhWaFE9PSIsInZhbHVlIjoiRTlGaDVKSTMxdm15ODBTM2tvZFdQRldxL3RNNnY4UXJBc3RDWGU3SUJMVFhKYnJ1OTM4NzZLb0FWVzBRTDdZTy9abzg1MXJuWTNBaW0wNElrMDd2NUtheFNvTlI4cWc4NlhVa3VoaXBHOTJJRDgybGQ2WXp1NVkxODBFVTliVzYyQ2Q5bEo4bVNrbjhDaVBmbDNNNE1BPT0iLCJtYWMiOiJmNzIxZjQzOWQ0ZDJiOGUxNTU3ODZlZmI3YzllYmI3MGRhNjQ3YWIxZTc2NGUyNTMzOTg4NjA1YmIyZGRiYzAwIiwidGFnIjoiIn0%3D; ci_session=f6e00a1558f51d5c8f64fc846864e6a2"}, "cookies": {"admin_auth": "eyJpdiI6IkQ3T3RUQngyc043Q3RZSWVnSjhWaFE9PSIsInZhbHVlIjoiRTlGaDVKSTMxdm15ODBTM2tvZFdQRldxL3RNNnY4UXJBc3RDWGU3SUJMVFhKYnJ1OTM4NzZLb0FWVzBRTDdZTy9abzg1MXJuWTNBaW0wNElrMDd2NUtheFNvTlI4cWc4NlhVa3VoaXBHOTJJRDgybGQ2WXp1NVkxODBFVTliVzYyQ2Q5bEo4bVNrbjhDaVBmbDNNNE1BPT0iLCJtYWMiOiJmNzIxZjQzOWQ0ZDJiOGUxNTU3ODZlZmI3YzllYmI3MGRhNjQ3YWIxZTc2NGUyNTMzOTg4NjA1YmIyZGRiYzAwIiwidGFnIjoiIn0=", "ci_session": "f6e00a1558f51d5c8f64fc846864e6a2"}, "request": "HTTP/1.1", "response": {"statusCode": 200, "reason": "OK", "contentType": "text/html; charset=UTF-8", "headers": {"Content-Type": "text/html; charset=UTF-8"}}}, "config": {"ciVersion": "4.6.1", "phpVersion": "8.2.12", "phpSAPI": "cli-server", "environment": "development", "baseURL": "http://localhost:8081/", "timezone": "UTC", "locale": "en", "cspEnabled": false}}