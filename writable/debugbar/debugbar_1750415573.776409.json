{"url": "http://localhost:8081/", "method": "GET", "isAJAX": false, "startTime": **********.644432, "totalTime": 85.7, "totalMemory": "6.455", "segmentDuration": 15, "segmentCount": 6, "CI_VERSION": "4.6.1", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.648623, "duration": 0.029767990112304688}, {"name": "Required Before Filters", "component": "Timer", "start": **********.678394, "duration": 0.0037539005279541016}, {"name": "Routing", "component": "Timer", "start": **********.682159, "duration": 0.0024580955505371094}, {"name": "Before Filters", "component": "Timer", "start": **********.684861, "duration": 4.00543212890625e-05}, {"name": "Controller", "component": "Timer", "start": **********.684913, "duration": 0.044634103775024414}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.684914, "duration": 0.018454790115356445}, {"name": "After Filters", "component": "Timer", "start": **********.729568, "duration": 8.106231689453125e-06}, {"name": "Required After Filters", "component": "Timer", "start": **********.729616, "duration": 0.0005600452423095703}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(6 total Queries, 6 of them unique across 1 Connection)", "display": {"queries": [{"hover": "", "class": "", "duration": "0.93 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `settings`\n<strong>WHERE</strong> `is_public` = 1", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/SettingsModel.php:118", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Home.php:28", "function": "        App\\Models\\SettingsModel->getPublicSettings()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Home->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}, {"file": "SYSTEMPATH/rewrite.php:44", "args": ["/opt/lampp/htdocs/cms/public/index.php"], "function": "        require_once()", "index": " 12    "}], "trace-file": "APPPATH/Models/SettingsModel.php:118", "qid": "f08e61ea947c167f8eb5b7e28d6d54f4"}, {"hover": "", "class": "", "duration": "0.9 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `menus`\n<strong>WHERE</strong> `location` = &#039;primary&#039;\n<strong>AND</strong> `status` = &#039;active&#039;\n <strong>LIMIT</strong> 1", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:330", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:717", "function": "        CodeIgniter\\Model->doFirst()", "index": "  3    "}, {"file": "APPPATH/Models/MenuModel.php:89", "function": "        CodeIgniter\\BaseModel->first()", "index": "  4    "}, {"file": "APPPATH/Controllers/Home.php:31", "function": "        App\\Models\\MenuModel->getMenuByLocation()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Home->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}, {"file": "SYSTEMPATH/rewrite.php:44", "args": ["/opt/lampp/htdocs/cms/public/index.php"], "function": "        require_once()", "index": " 12    "}], "trace-file": "APPPATH/Models/MenuModel.php:89", "qid": "301b8e6e28f7478c7be8fb76ec896a79"}, {"hover": "", "class": "", "duration": "1.26 ms", "sql": "<strong>SELECT</strong> `menu_items`.*, `pages`.`title` as `page_title`, `pages`.`slug` as `page_slug`\n<strong>FROM</strong> `menu_items`\n<strong>LEFT</strong> <strong>JOIN</strong> `pages` <strong>ON</strong> `pages`.`id` = `menu_items`.`page_id`\n<strong>WHERE</strong> `menu_items`.`menu_id` = &#039;1&#039;\n<strong>AND</strong> `menu_items`.`status` = &#039;active&#039;\n<strong>ORDER</strong> <strong>BY</strong> `menu_items`.`sort_order` <strong>ASC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/MenuItemModel.php:67", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Models/MenuModel.php:96", "function": "        App\\Models\\MenuItemModel->getMenuItems()", "index": "  5    "}, {"file": "APPPATH/Controllers/Home.php:31", "function": "        App\\Models\\MenuModel->getMenuByLocation()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Home->index()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH/index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}, {"file": "SYSTEMPATH/rewrite.php:44", "args": ["/opt/lampp/htdocs/cms/public/index.php"], "function": "        require_once()", "index": " 13    "}], "trace-file": "APPPATH/Models/MenuItemModel.php:67", "qid": "bc830dac70b6b9ca3bed56ae1d773fcc"}, {"hover": "", "class": "", "duration": "0.83 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `menus`\n<strong>WHERE</strong> `location` = &#039;footer&#039;\n<strong>AND</strong> `status` = &#039;active&#039;\n <strong>LIMIT</strong> 1", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:330", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:717", "function": "        CodeIgniter\\Model->doFirst()", "index": "  3    "}, {"file": "APPPATH/Models/MenuModel.php:89", "function": "        CodeIgniter\\BaseModel->first()", "index": "  4    "}, {"file": "APPPATH/Controllers/Home.php:35", "function": "        App\\Models\\MenuModel->getMenuByLocation()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Home->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}, {"file": "SYSTEMPATH/rewrite.php:44", "args": ["/opt/lampp/htdocs/cms/public/index.php"], "function": "        require_once()", "index": " 12    "}], "trace-file": "APPPATH/Models/MenuModel.php:89", "qid": "0577de717ada1068247af773670f01ce"}, {"hover": "", "class": "", "duration": "1.19 ms", "sql": "<strong>SELECT</strong> `menu_items`.*, `pages`.`title` as `page_title`, `pages`.`slug` as `page_slug`\n<strong>FROM</strong> `menu_items`\n<strong>LEFT</strong> <strong>JOIN</strong> `pages` <strong>ON</strong> `pages`.`id` = `menu_items`.`page_id`\n<strong>WHERE</strong> `menu_items`.`menu_id` = &#039;2&#039;\n<strong>AND</strong> `menu_items`.`status` = &#039;active&#039;\n<strong>ORDER</strong> <strong>BY</strong> `menu_items`.`sort_order` <strong>ASC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/MenuItemModel.php:67", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Models/MenuModel.php:96", "function": "        App\\Models\\MenuItemModel->getMenuItems()", "index": "  5    "}, {"file": "APPPATH/Controllers/Home.php:35", "function": "        App\\Models\\MenuModel->getMenuByLocation()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Home->index()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH/index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}, {"file": "SYSTEMPATH/rewrite.php:44", "args": ["/opt/lampp/htdocs/cms/public/index.php"], "function": "        require_once()", "index": " 13    "}], "trace-file": "APPPATH/Models/MenuItemModel.php:67", "qid": "0b4f0f8e14b7afc2839dc9145b0c098f"}, {"hover": "", "class": "", "duration": "0.58 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `pages`\n<strong>WHERE</strong> `slug` = &#039;home&#039;\n<strong>AND</strong> `status` = &#039;published&#039;\n <strong>LIMIT</strong> 1", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:330", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:717", "function": "        CodeIgniter\\Model->doFirst()", "index": "  3    "}, {"file": "APPPATH/Controllers/Home.php:39", "function": "        CodeIgniter\\BaseModel->first()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Home->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}, {"file": "SYSTEMPATH/rewrite.php:44", "args": ["/opt/lampp/htdocs/cms/public/index.php"], "function": "        require_once()", "index": " 11    "}], "trace-file": "APPPATH/Controllers/Home.php:39", "qid": "53901924a8c5418defe2b5e9236ab123"}]}, "badgeValue": 6, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": **********.712317, "duration": "0.000798"}, {"name": "Query", "component": "Database", "start": **********.714251, "duration": "0.000934", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `settings`\n<strong>WHERE</strong> `is_public` = 1"}, {"name": "Query", "component": "Database", "start": **********.717271, "duration": "0.000896", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `menus`\n<strong>WHERE</strong> `location` = &#039;primary&#039;\n<strong>AND</strong> `status` = &#039;active&#039;\n <strong>LIMIT</strong> 1"}, {"name": "Query", "component": "Database", "start": **********.718671, "duration": "0.001262", "query": "<strong>SELECT</strong> `menu_items`.*, `pages`.`title` as `page_title`, `pages`.`slug` as `page_slug`\n<strong>FROM</strong> `menu_items`\n<strong>LEFT</strong> <strong>JOIN</strong> `pages` <strong>ON</strong> `pages`.`id` = `menu_items`.`page_id`\n<strong>WHERE</strong> `menu_items`.`menu_id` = &#039;1&#039;\n<strong>AND</strong> `menu_items`.`status` = &#039;active&#039;\n<strong>ORDER</strong> <strong>BY</strong> `menu_items`.`sort_order` <strong>ASC</strong>"}, {"name": "Query", "component": "Database", "start": **********.720299, "duration": "0.000828", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `menus`\n<strong>WHERE</strong> `location` = &#039;footer&#039;\n<strong>AND</strong> `status` = &#039;active&#039;\n <strong>LIMIT</strong> 1"}, {"name": "Query", "component": "Database", "start": **********.721571, "duration": "0.001190", "query": "<strong>SELECT</strong> `menu_items`.*, `pages`.`title` as `page_title`, `pages`.`slug` as `page_slug`\n<strong>FROM</strong> `menu_items`\n<strong>LEFT</strong> <strong>JOIN</strong> `pages` <strong>ON</strong> `pages`.`id` = `menu_items`.`page_id`\n<strong>WHERE</strong> `menu_items`.`menu_id` = &#039;2&#039;\n<strong>AND</strong> `menu_items`.`status` = &#039;active&#039;\n<strong>ORDER</strong> <strong>BY</strong> `menu_items`.`sort_order` <strong>ASC</strong>"}, {"name": "Query", "component": "Database", "start": **********.723061, "duration": "0.000580", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `pages`\n<strong>WHERE</strong> `slug` = &#039;home&#039;\n<strong>AND</strong> `status` = &#039;published&#039;\n <strong>LIMIT</strong> 1"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": []}, "badgeValue": null, "isEmpty": true, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": [], "badgeValue": 2, "isEmpty": false, "hasTabContent": false, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "View: frontend/layouts/default.php", "component": "Views", "start": **********.72688, "duration": 0.002566814422607422}, {"name": "View: frontend/templates/home.php", "component": "Views", "start": **********.726128, "duration": 0.0033648014068603516}]}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 170 )", "display": {"coreFiles": [{"path": "SYSTEMPATH/API/ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH/Autoloader/Autoloader.php", "name": "Autoloader.php"}, {"path": "SYSTEMPATH/Autoloader/FileLocator.php", "name": "FileLocator.php"}, {"path": "SYSTEMPATH/Autoloader/FileLocatorInterface.php", "name": "FileLocatorInterface.php"}, {"path": "SYSTEMPATH/BaseModel.php", "name": "BaseModel.php"}, {"path": "SYSTEMPATH/Boot.php", "name": "Boot.php"}, {"path": "SYSTEMPATH/Cache/CacheFactory.php", "name": "CacheFactory.php"}, {"path": "SYSTEMPATH/Cache/CacheInterface.php", "name": "CacheInterface.php"}, {"path": "SYSTEMPATH/Cache/Handlers/BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH/Cache/Handlers/FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH/Cache/ResponseCache.php", "name": "ResponseCache.php"}, {"path": "SYSTEMPATH/CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "SYSTEMPATH/Common.php", "name": "Common.php"}, {"path": "SYSTEMPATH/Config/AutoloadConfig.php", "name": "AutoloadConfig.php"}, {"path": "SYSTEMPATH/Config/BaseConfig.php", "name": "BaseConfig.php"}, {"path": "SYSTEMPATH/Config/BaseService.php", "name": "BaseService.php"}, {"path": "SYSTEMPATH/Config/DotEnv.php", "name": "DotEnv.php"}, {"path": "SYSTEMPATH/Config/Factories.php", "name": "Factories.php"}, {"path": "SYSTEMPATH/Config/Factory.php", "name": "Factory.php"}, {"path": "SYSTEMPATH/Config/Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH/Config/Routing.php", "name": "Routing.php"}, {"path": "SYSTEMPATH/Config/Services.php", "name": "Services.php"}, {"path": "SYSTEMPATH/Config/View.php", "name": "View.php"}, {"path": "SYSTEMPATH/Controller.php", "name": "Controller.php"}, {"path": "SYSTEMPATH/Cookie/CloneableCookieInterface.php", "name": "CloneableCookieInterface.php"}, {"path": "SYSTEMPATH/Cookie/Cookie.php", "name": "Cookie.php"}, {"path": "SYSTEMPATH/Cookie/CookieInterface.php", "name": "CookieInterface.php"}, {"path": "SYSTEMPATH/Cookie/CookieStore.php", "name": "CookieStore.php"}, {"path": "SYSTEMPATH/DataCaster/Cast/ArrayCast.php", "name": "ArrayCast.php"}, {"path": "SYSTEMPATH/DataCaster/Cast/BaseCast.php", "name": "BaseCast.php"}, {"path": "SYSTEMPATH/DataCaster/Cast/BooleanCast.php", "name": "BooleanCast.php"}, {"path": "SYSTEMPATH/DataCaster/Cast/CSVCast.php", "name": "CSVCast.php"}, {"path": "SYSTEMPATH/DataCaster/Cast/CastInterface.php", "name": "CastInterface.php"}, {"path": "SYSTEMPATH/DataCaster/Cast/DatetimeCast.php", "name": "DatetimeCast.php"}, {"path": "SYSTEMPATH/DataCaster/Cast/FloatCast.php", "name": "FloatCast.php"}, {"path": "SYSTEMPATH/DataCaster/Cast/IntBoolCast.php", "name": "IntBoolCast.php"}, {"path": "SYSTEMPATH/DataCaster/Cast/IntegerCast.php", "name": "IntegerCast.php"}, {"path": "SYSTEMPATH/DataCaster/Cast/JsonCast.php", "name": "JsonCast.php"}, {"path": "SYSTEMPATH/DataCaster/Cast/TimestampCast.php", "name": "TimestampCast.php"}, {"path": "SYSTEMPATH/DataCaster/Cast/URICast.php", "name": "URICast.php"}, {"path": "SYSTEMPATH/DataCaster/DataCaster.php", "name": "DataCaster.php"}, {"path": "SYSTEMPATH/DataConverter/DataConverter.php", "name": "DataConverter.php"}, {"path": "SYSTEMPATH/Database/BaseBuilder.php", "name": "BaseBuilder.php"}, {"path": "SYSTEMPATH/Database/BaseConnection.php", "name": "BaseConnection.php"}, {"path": "SYSTEMPATH/Database/BaseResult.php", "name": "BaseResult.php"}, {"path": "SYSTEMPATH/Database/Config.php", "name": "Config.php"}, {"path": "SYSTEMPATH/Database/ConnectionInterface.php", "name": "ConnectionInterface.php"}, {"path": "SYSTEMPATH/Database/Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH/Database/MySQLi/Builder.php", "name": "Builder.php"}, {"path": "SYSTEMPATH/Database/MySQLi/Connection.php", "name": "Connection.php"}, {"path": "SYSTEMPATH/Database/MySQLi/Result.php", "name": "Result.php"}, {"path": "SYSTEMPATH/Database/Query.php", "name": "Query.php"}, {"path": "SYSTEMPATH/Database/QueryInterface.php", "name": "QueryInterface.php"}, {"path": "SYSTEMPATH/Database/ResultInterface.php", "name": "ResultInterface.php"}, {"path": "SYSTEMPATH/Debug/Exceptions.php", "name": "Exceptions.php"}, {"path": "SYSTEMPATH/Debug/Timer.php", "name": "Timer.php"}, {"path": "SYSTEMPATH/Debug/Toolbar.php", "name": "Toolbar.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/BaseCollector.php", "name": "BaseCollector.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Files.php", "name": "Files.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Logs.php", "name": "Logs.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Routes.php", "name": "Routes.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Timers.php", "name": "Timers.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Views.php", "name": "Views.php"}, {"path": "SYSTEMPATH/Events/Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH/Filters/DebugToolbar.php", "name": "DebugToolbar.php"}, {"path": "SYSTEMPATH/Filters/FilterInterface.php", "name": "FilterInterface.php"}, {"path": "SYSTEMPATH/Filters/Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH/Filters/ForceHTTPS.php", "name": "ForceHTTPS.php"}, {"path": "SYSTEMPATH/Filters/PageCache.php", "name": "PageCache.php"}, {"path": "SYSTEMPATH/Filters/PerformanceMetrics.php", "name": "PerformanceMetrics.php"}, {"path": "SYSTEMPATH/HTTP/ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "SYSTEMPATH/HTTP/Header.php", "name": "Header.php"}, {"path": "SYSTEMPATH/HTTP/IncomingRequest.php", "name": "IncomingRequest.php"}, {"path": "SYSTEMPATH/HTTP/Message.php", "name": "Message.php"}, {"path": "SYSTEMPATH/HTTP/MessageInterface.php", "name": "MessageInterface.php"}, {"path": "SYSTEMPATH/HTTP/MessageTrait.php", "name": "MessageTrait.php"}, {"path": "SYSTEMPATH/HTTP/Method.php", "name": "Method.php"}, {"path": "SYSTEMPATH/HTTP/OutgoingRequest.php", "name": "OutgoingRequest.php"}, {"path": "SYSTEMPATH/HTTP/OutgoingRequestInterface.php", "name": "OutgoingRequestInterface.php"}, {"path": "SYSTEMPATH/HTTP/Request.php", "name": "Request.php"}, {"path": "SYSTEMPATH/HTTP/RequestInterface.php", "name": "RequestInterface.php"}, {"path": "SYSTEMPATH/HTTP/RequestTrait.php", "name": "RequestTrait.php"}, {"path": "SYSTEMPATH/HTTP/Response.php", "name": "Response.php"}, {"path": "SYSTEMPATH/HTTP/ResponseInterface.php", "name": "ResponseInterface.php"}, {"path": "SYSTEMPATH/HTTP/ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH/HTTP/SiteURI.php", "name": "SiteURI.php"}, {"path": "SYSTEMPATH/HTTP/SiteURIFactory.php", "name": "SiteURIFactory.php"}, {"path": "SYSTEMPATH/HTTP/URI.php", "name": "URI.php"}, {"path": "SYSTEMPATH/HTTP/UserAgent.php", "name": "UserAgent.php"}, {"path": "SYSTEMPATH/Helpers/array_helper.php", "name": "array_helper.php"}, {"path": "SYSTEMPATH/Helpers/filesystem_helper.php", "name": "filesystem_helper.php"}, {"path": "SYSTEMPATH/Helpers/kint_helper.php", "name": "kint_helper.php"}, {"path": "SYSTEMPATH/Helpers/url_helper.php", "name": "url_helper.php"}, {"path": "SYSTEMPATH/I18n/Time.php", "name": "Time.php"}, {"path": "SYSTEMPATH/I18n/TimeTrait.php", "name": "TimeTrait.php"}, {"path": "SYSTEMPATH/Log/Logger.php", "name": "Logger.php"}, {"path": "SYSTEMPATH/Model.php", "name": "Model.php"}, {"path": "SYSTEMPATH/Modules/Modules.php", "name": "Modules.php"}, {"path": "SYSTEMPATH/Router/RouteCollection.php", "name": "RouteCollection.php"}, {"path": "SYSTEMPATH/Router/RouteCollectionInterface.php", "name": "RouteCollectionInterface.php"}, {"path": "SYSTEMPATH/Router/Router.php", "name": "Router.php"}, {"path": "SYSTEMPATH/Router/RouterInterface.php", "name": "RouterInterface.php"}, {"path": "SYSTEMPATH/Superglobals.php", "name": "Superglobals.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/FacadeInterface.php", "name": "FacadeInterface.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Kint.php", "name": "Kint.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/AbstractRenderer.php", "name": "AbstractRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/AssetRendererTrait.php", "name": "AssetRendererTrait.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/CliRenderer.php", "name": "CliRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/ConstructableRendererInterface.php", "name": "ConstructableRendererInterface.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/RichRenderer.php", "name": "RichRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/TextRenderer.php", "name": "TextRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Utils.php", "name": "Utils.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/init.php", "name": "init.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/init_helpers.php", "name": "init_helpers.php"}, {"path": "SYSTEMPATH/Traits/ConditionalTrait.php", "name": "ConditionalTrait.php"}, {"path": "SYSTEMPATH/View/RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH/View/View.php", "name": "View.php"}, {"path": "SYSTEMPATH/View/ViewDecoratorTrait.php", "name": "ViewDecoratorTrait.php"}, {"path": "SYSTEMPATH/rewrite.php", "name": "rewrite.php"}], "userFiles": [{"path": "APPPATH/Common.php", "name": "Common.php"}, {"path": "APPPATH/Config/App.php", "name": "App.php"}, {"path": "APPPATH/Config/Autoload.php", "name": "Autoload.php"}, {"path": "APPPATH/Config/Boot/development.php", "name": "development.php"}, {"path": "APPPATH/Config/Cache.php", "name": "Cache.php"}, {"path": "APPPATH/Config/Constants.php", "name": "Constants.php"}, {"path": "APPPATH/Config/ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "APPPATH/Config/Cookie.php", "name": "Cookie.php"}, {"path": "APPPATH/Config/Database.php", "name": "Database.php"}, {"path": "APPPATH/Config/Events.php", "name": "Events.php"}, {"path": "APPPATH/Config/Exceptions.php", "name": "Exceptions.php"}, {"path": "APPPATH/Config/Feature.php", "name": "Feature.php"}, {"path": "APPPATH/Config/Filters.php", "name": "Filters.php"}, {"path": "APPPATH/Config/Kint.php", "name": "Kint.php"}, {"path": "APPPATH/Config/Logger.php", "name": "Logger.php"}, {"path": "APPPATH/Config/Modules.php", "name": "Modules.php"}, {"path": "APPPATH/Config/Optimize.php", "name": "Optimize.php"}, {"path": "APPPATH/Config/Paths.php", "name": "Paths.php"}, {"path": "APPPATH/Config/Routes.php", "name": "Routes.php"}, {"path": "APPPATH/Config/Routing.php", "name": "Routing.php"}, {"path": "APPPATH/Config/Services.php", "name": "Services.php"}, {"path": "APPPATH/Config/Toolbar.php", "name": "Toolbar.php"}, {"path": "APPPATH/Config/UserAgents.php", "name": "UserAgents.php"}, {"path": "APPPATH/Config/View.php", "name": "View.php"}, {"path": "APPPATH/Controllers/BaseController.php", "name": "BaseController.php"}, {"path": "APPPATH/Controllers/Home.php", "name": "Home.php"}, {"path": "APPPATH/Helpers/seo_helper.php", "name": "seo_helper.php"}, {"path": "APPPATH/Models/MenuItemModel.php", "name": "MenuItemModel.php"}, {"path": "APPPATH/Models/MenuModel.php", "name": "MenuModel.php"}, {"path": "APPPATH/Models/PageModel.php", "name": "PageModel.php"}, {"path": "APPPATH/Models/SettingsModel.php", "name": "SettingsModel.php"}, {"path": "APPPATH/Views/frontend/layouts/default.php", "name": "default.php"}, {"path": "APPPATH/Views/frontend/templates/home.php", "name": "home.php"}, {"path": "FCPATH/index.php", "name": "index.php"}, {"path": "VENDORPATH/autoload.php", "name": "autoload.php"}, {"path": "VENDORPATH/composer/ClassLoader.php", "name": "ClassLoader.php"}, {"path": "VENDORPATH/composer/InstalledVersions.php", "name": "InstalledVersions.php"}, {"path": "VENDORPATH/composer/autoload_real.php", "name": "autoload_real.php"}, {"path": "VENDORPATH/composer/autoload_static.php", "name": "autoload_static.php"}, {"path": "VENDORPATH/composer/installed.php", "name": "installed.php"}, {"path": "VENDORPATH/composer/platform_check.php", "name": "platform_check.php"}, {"path": "VENDORPATH/laminas/laminas-escaper/src/Escaper.php", "name": "Escaper.php"}, {"path": "VENDORPATH/laminas/laminas-escaper/src/EscaperInterface.php", "name": "EscaperInterface.php"}, {"path": "VENDORPATH/myclabs/deep-copy/src/DeepCopy/deep_copy.php", "name": "deep_copy.php"}, {"path": "VENDORPATH/phpunit/phpunit/src/Framework/Assert/Functions.php", "name": "Functions.php"}, {"path": "VENDORPATH/psr/log/src/LogLevel.php", "name": "LogLevel.php"}, {"path": "VENDORPATH/psr/log/src/LoggerInterface.php", "name": "LoggerInterface.php"}, {"path": "VENDORPATH/symfony/deprecation-contracts/function.php", "name": "function.php"}]}, "badgeValue": 170, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"directory": "", "controller": "\\App\\Controllers\\Home", "method": "index", "paramCount": 0, "truePCount": 0, "params": []}], "routes": [{"method": "GET", "route": "/", "handler": "\\App\\Controllers\\Home::index"}, {"method": "GET", "route": "page/([^/]+)", "handler": "\\App\\Controllers\\Home::page/$1"}, {"method": "GET", "route": "blog", "handler": "\\App\\Controllers\\BlogController::index"}, {"method": "GET", "route": "blog/page/([0-9]+)", "handler": "\\App\\Controllers\\BlogController::index/$1"}, {"method": "GET", "route": "blog/([^/]+)", "handler": "\\App\\Controllers\\BlogController::post/$1"}, {"method": "GET", "route": "category/([^/]+)", "handler": "\\App\\Controllers\\BlogController::category/$1"}, {"method": "GET", "route": "category/([^/]+)/page/([0-9]+)", "handler": "\\App\\Controllers\\BlogController::category/$1/$2"}, {"method": "GET", "route": "tag/([^/]+)", "handler": "\\App\\Controllers\\BlogController::tag/$1"}, {"method": "GET", "route": "tag/([^/]+)/page/([0-9]+)", "handler": "\\App\\Controllers\\BlogController::tag/$1/$2"}, {"method": "GET", "route": "sitemap.xml", "handler": "\\App\\Controllers\\SitemapController::index"}, {"method": "GET", "route": "robots.txt", "handler": "\\App\\Controllers\\SitemapController::robots"}, {"method": "GET", "route": "rss.xml", "handler": "\\App\\Controllers\\SitemapController::rss"}, {"method": "GET", "route": "feed.json", "handler": "\\App\\Controllers\\SitemapController::jsonFeed"}, {"method": "GET", "route": "manifest.json", "handler": "\\App\\Controllers\\SitemapController::manifest"}, {"method": "GET", "route": "auth/login", "handler": "\\App\\Controllers\\AuthController::login"}, {"method": "GET", "route": "auth/register", "handler": "\\App\\Controllers\\AuthController::register"}, {"method": "GET", "route": "auth/logout", "handler": "\\App\\Controllers\\AuthController::logout"}, {"method": "GET", "route": "auth/check", "handler": "\\App\\Controllers\\AuthController::checkAuth"}, {"method": "GET", "route": "user/dashboard", "handler": "\\App\\Controllers\\UserDashboardController::index"}, {"method": "GET", "route": "user/profile", "handler": "\\App\\Controllers\\UserDashboardController::profile"}, {"method": "GET", "route": "user/posts", "handler": "\\App\\Controllers\\UserDashboardController::myPosts"}, {"method": "GET", "route": "user/pages", "handler": "\\App\\Controllers\\UserDashboardController::myPages"}, {"method": "GET", "route": "user/media", "handler": "\\App\\Controllers\\UserDashboardController::myMedia"}, {"method": "GET", "route": "user/settings", "handler": "\\App\\Controllers\\UserDashboardController::settings"}, {"method": "GET", "route": "admin/dashboard", "handler": "\\App\\Controllers\\Admin\\DashboardController::index"}, {"method": "GET", "route": "admin/pages", "handler": "\\App\\Controllers\\Admin\\PageController::index"}, {"method": "GET", "route": "admin/pages/create", "handler": "\\App\\Controllers\\Admin\\PageController::create"}, {"method": "GET", "route": "admin/pages/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\PageController::edit/$1"}, {"method": "GET", "route": "admin/pages/preview/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\PageController::preview/$1"}, {"method": "GET", "route": "admin/menus", "handler": "\\App\\Controllers\\Admin\\MenuController::index"}, {"method": "GET", "route": "admin/menus/create", "handler": "\\App\\Controllers\\Admin\\MenuController::create"}, {"method": "GET", "route": "admin/menus/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\MenuController::edit/$1"}, {"method": "GET", "route": "admin/menus/builder/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\MenuController::builder/$1"}, {"method": "GET", "route": "admin/menu-items/get/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\MenuItemController::get/$1"}, {"method": "GET", "route": "admin/menu-items/menu/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\MenuItemController::getMenuItems/$1"}, {"method": "GET", "route": "admin/menu-items/search-pages", "handler": "\\App\\Controllers\\Admin\\MenuItemController::searchPages"}, {"method": "GET", "route": "admin/blog", "handler": "\\App\\Controllers\\Admin\\BlogController::index"}, {"method": "GET", "route": "admin/blog/create", "handler": "\\App\\Controllers\\Admin\\BlogController::create"}, {"method": "GET", "route": "admin/blog/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\BlogController::edit/$1"}, {"method": "GET", "route": "admin/blog/search", "handler": "\\App\\Controllers\\Admin\\BlogController::search"}, {"method": "GET", "route": "admin/blog/by-status", "handler": "\\App\\Controllers\\Admin\\BlogController::getPostsByStatus"}, {"method": "GET", "route": "admin/categories", "handler": "\\App\\Controllers\\Admin\\CategoryController::index"}, {"method": "GET", "route": "admin/categories/create", "handler": "\\App\\Controllers\\Admin\\CategoryController::create"}, {"method": "GET", "route": "admin/categories/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\CategoryController::edit/$1"}, {"method": "GET", "route": "admin/tags", "handler": "\\App\\Controllers\\Admin\\TagController::index"}, {"method": "GET", "route": "admin/tags/create", "handler": "\\App\\Controllers\\Admin\\TagController::create"}, {"method": "GET", "route": "admin/tags/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\TagController::edit/$1"}, {"method": "GET", "route": "admin/tags/search", "handler": "\\App\\Controllers\\Admin\\TagController::search"}, {"method": "GET", "route": "admin/comments", "handler": "\\App\\Controllers\\Admin\\CommentController::index"}, {"method": "GET", "route": "admin/comments/pending", "handler": "\\App\\Controllers\\Admin\\CommentController::pending"}, {"method": "GET", "route": "admin/media", "handler": "\\App\\Controllers\\Admin\\MediaController::index"}, {"method": "GET", "route": "admin/media/get-media", "handler": "\\App\\Controllers\\Admin\\MediaController::getMedia"}, {"method": "GET", "route": "admin/media/details/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\MediaController::details/$1"}, {"method": "GET", "route": "admin/media/picker", "handler": "\\App\\Controllers\\Admin\\MediaController::picker"}, {"method": "GET", "route": "admin/media/stats", "handler": "\\App\\Controllers\\Admin\\MediaController::stats"}, {"method": "GET", "route": "admin/themes", "handler": "\\App\\Controllers\\Admin\\ThemeController::index"}, {"method": "GET", "route": "admin/themes/create", "handler": "\\App\\Controllers\\Admin\\ThemeController::create"}, {"method": "GET", "route": "admin/themes/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\ThemeController::edit/$1"}, {"method": "GET", "route": "admin/themes/settings", "handler": "\\App\\Controllers\\Admin\\ThemeController::settings"}, {"method": "GET", "route": "admin/users", "handler": "\\App\\Controllers\\Admin\\UserController::index"}, {"method": "GET", "route": "admin/users/create", "handler": "\\App\\Controllers\\Admin\\UserController::create"}, {"method": "GET", "route": "admin/users/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\UserController::edit/$1"}, {"method": "GET", "route": "admin/users/profile/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\UserController::profile/$1"}, {"method": "POST", "route": "blog/comment", "handler": "\\App\\Controllers\\BlogController::submitComment"}, {"method": "POST", "route": "auth/login", "handler": "\\App\\Controllers\\AuthController::processLogin"}, {"method": "POST", "route": "auth/register", "handler": "\\App\\Controllers\\AuthController::processRegister"}, {"method": "POST", "route": "user/profile", "handler": "\\App\\Controllers\\UserDashboardController::updateProfile"}, {"method": "POST", "route": "admin/pages/store", "handler": "\\App\\Controllers\\Admin\\PageController::store"}, {"method": "POST", "route": "admin/pages/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\PageController::update/$1"}, {"method": "POST", "route": "admin/menus/store", "handler": "\\App\\Controllers\\Admin\\MenuController::store"}, {"method": "POST", "route": "admin/menus/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\MenuController::update/$1"}, {"method": "POST", "route": "admin/menus/duplicate/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\MenuController::duplicate/$1"}, {"method": "POST", "route": "admin/menu-items/store", "handler": "\\App\\Controllers\\Admin\\MenuItemController::store"}, {"method": "POST", "route": "admin/menu-items/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\MenuItemController::update/$1"}, {"method": "POST", "route": "admin/menu-items/update-order", "handler": "\\App\\Controllers\\Admin\\MenuItemController::updateOrder"}, {"method": "POST", "route": "admin/menu-items/bulk-status", "handler": "\\App\\Controllers\\Admin\\MenuItemController::bulkUpdateStatus"}, {"method": "POST", "route": "admin/blog/store", "handler": "\\App\\Controllers\\Admin\\BlogController::store"}, {"method": "POST", "route": "admin/blog/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\BlogController::update/$1"}, {"method": "POST", "route": "admin/blog/duplicate/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\BlogController::duplicate/$1"}, {"method": "POST", "route": "admin/blog/bulk-action", "handler": "\\App\\Controllers\\Admin\\BlogController::bulkAction"}, {"method": "POST", "route": "admin/categories/store", "handler": "\\App\\Controllers\\Admin\\CategoryController::store"}, {"method": "POST", "route": "admin/categories/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\CategoryController::update/$1"}, {"method": "POST", "route": "admin/categories/reorder", "handler": "\\App\\Controllers\\Admin\\CategoryController::reorder"}, {"method": "POST", "route": "admin/tags/store", "handler": "\\App\\Controllers\\Admin\\TagController::store"}, {"method": "POST", "route": "admin/tags/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\TagController::update/$1"}, {"method": "POST", "route": "admin/comments/approve/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\CommentController::approve/$1"}, {"method": "POST", "route": "admin/comments/spam/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\CommentController::spam/$1"}, {"method": "POST", "route": "admin/comments/trash/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\CommentController::trash/$1"}, {"method": "POST", "route": "admin/comments/bulk-action", "handler": "\\App\\Controllers\\Admin\\CommentController::bulkAction"}, {"method": "POST", "route": "admin/media/upload", "handler": "\\App\\Controllers\\Admin\\MediaController::upload"}, {"method": "POST", "route": "admin/media/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\MediaController::update/$1"}, {"method": "POST", "route": "admin/media/bulk-delete", "handler": "\\App\\Controllers\\Admin\\MediaController::bulkDelete"}, {"method": "POST", "route": "admin/media/generate-thumbnails", "handler": "\\App\\Controllers\\Admin\\MediaController::generateThumbnails"}, {"method": "POST", "route": "admin/media/cleanup", "handler": "\\App\\Controllers\\Admin\\MediaController::cleanup"}, {"method": "POST", "route": "admin/themes/store", "handler": "\\App\\Controllers\\Admin\\ThemeController::store"}, {"method": "POST", "route": "admin/themes/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\ThemeController::update/$1"}, {"method": "POST", "route": "admin/themes/set-default/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\ThemeController::setDefault/$1"}, {"method": "POST", "route": "admin/themes/settings", "handler": "\\App\\Controllers\\Admin\\ThemeController::updateSettings"}, {"method": "POST", "route": "admin/users/store", "handler": "\\App\\Controllers\\Admin\\UserController::store"}, {"method": "POST", "route": "admin/users/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\UserController::update/$1"}, {"method": "POST", "route": "admin/users/toggle-status/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\UserController::toggleStatus/$1"}, {"method": "POST", "route": "admin/users/bulk-action", "handler": "\\App\\Controllers\\Admin\\UserController::bulkAction"}, {"method": "DELETE", "route": "admin/pages/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\PageController::delete/$1"}, {"method": "DELETE", "route": "admin/menus/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\MenuController::delete/$1"}, {"method": "DELETE", "route": "admin/menu-items/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\MenuItemController::delete/$1"}, {"method": "DELETE", "route": "admin/blog/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\BlogController::delete/$1"}, {"method": "DELETE", "route": "admin/categories/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\CategoryController::delete/$1"}, {"method": "DELETE", "route": "admin/tags/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\TagController::delete/$1"}, {"method": "DELETE", "route": "admin/comments/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\CommentController::delete/$1"}, {"method": "DELETE", "route": "admin/media/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\MediaController::delete/$1"}, {"method": "DELETE", "route": "admin/themes/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\ThemeController::delete/$1"}, {"method": "DELETE", "route": "admin/users/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\UserController::delete/$1"}]}, "badgeValue": 64, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "12.95", "count": 1}, "DBQuery": {"event": "DB<PERSON>uery", "duration": "0.30", "count": 6}}}, "badgeValue": 7, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.665436, "duration": 0.01294708251953125}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.715215, "duration": 5.1975250244140625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.718175, "duration": 5.602836608886719e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.719943, "duration": 5.1021575927734375e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.721135, "duration": 3.719329833984375e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.722768, "duration": 3.886222839355469e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.723648, "duration": 6.103515625e-05}]}], "vars": {"varData": {"View Data": {"page_title": "Welcome", "site_name": "My CMS Website", "site_description": "A powerful content management system", "menu_items": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (5)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (5)</li><li>Contents (5)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>id</th><th>menu_id</th><th>parent_id</th><th>title</th><th>url</th><th>page_id</th><th>type</th><th>target</th><th>css_class</th><th>icon</th><th>sort_order</th><th>status</th><th>created_at</th><th>updated_at</th><th>page_title</th><th>page_slug</th><th>children</th></tr></thead><tbody><tr><th>0</th><td title=\"string (1)\">1</td><td title=\"string (1)\">1</td><td title=\"null\"><var>null</var></td><td title=\"string (4)\">Home</td><td title=\"string (1)\">/</td><td title=\"null\"><var>null</var></td><td title=\"string (6)\">custom</td><td title=\"string (5)\">_self</td><td title=\"string (0)\"></td><td title=\"string (11)\">fas fa-home</td><td title=\"string (1)\">1</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-06-20 09:44:06</td><td title=\"string (19)\">2025-06-20 09:44:06</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"array (0)\"><var>array</var> (0)</td></tr><tr><th>1</th><td title=\"string (1)\">2</td><td title=\"string (1)\">1</td><td title=\"null\"><var>null</var></td><td title=\"string (5)\">About</td><td title=\"string (11)\">/page/about</td><td title=\"null\"><var>null</var></td><td title=\"string (6)\">custom</td><td title=\"string (5)\">_self</td><td title=\"string (0)\"></td><td title=\"string (18)\">fas fa-info-circle</td><td title=\"string (1)\">2</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-06-20 09:44:06</td><td title=\"string (19)\">2025-06-20 09:44:06</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"array (0)\"><var>array</var> (0)</td></tr><tr><th>2</th><td title=\"string (1)\">3</td><td title=\"string (1)\">1</td><td title=\"null\"><var>null</var></td><td title=\"string (4)\">Blog</td><td title=\"string (5)\">/blog</td><td title=\"null\"><var>null</var></td><td title=\"string (6)\">custom</td><td title=\"string (5)\">_self</td><td title=\"string (0)\"></td><td title=\"string (11)\">fas fa-blog</td><td title=\"string (1)\">3</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-06-20 09:44:06</td><td title=\"string (19)\">2025-06-20 09:44:06</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"array (0)\"><var>array</var> (0)</td></tr><tr><th>3</th><td title=\"string (1)\">4</td><td title=\"string (1)\">1</td><td title=\"null\"><var>null</var></td><td title=\"string (8)\">Services</td><td title=\"string (1)\">#</td><td title=\"null\"><var>null</var></td><td title=\"string (6)\">custom</td><td title=\"string (5)\">_self</td><td title=\"string (0)\"></td><td title=\"string (11)\">fas fa-cogs</td><td title=\"string (1)\">4</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-06-20 09:44:06</td><td title=\"string (19)\">2025-06-20 09:44:06</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"array (3)\"><var>array</var> (3)</td></tr><tr><th>4</th><td title=\"string (1)\">5</td><td title=\"string (1)\">1</td><td title=\"null\"><var>null</var></td><td title=\"string (7)\">Contact</td><td title=\"string (13)\">/page/contact</td><td title=\"null\"><var>null</var></td><td title=\"string (6)\">custom</td><td title=\"string (5)\">_self</td><td title=\"string (0)\"></td><td title=\"string (15)\">fas fa-envelope</td><td title=\"string (1)\">5</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-06-20 09:44:06</td><td title=\"string (19)\">2025-06-20 09:44:06</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"array (0)\"><var>array</var> (0)</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>menu_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['menu_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>parent_id</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['parent_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (4) \"Home\"<div class=\"access-path\">$value[0]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>url</dfn> =&gt; <var>string</var> (1) \"/\"<div class=\"access-path\">$value[0]['url']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>page_id</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['page_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>type</dfn> =&gt; <var>string</var> (6) \"custom\"<div class=\"access-path\">$value[0]['type']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>target</dfn> =&gt; <var>string</var> (5) \"_self\"<div class=\"access-path\">$value[0]['target']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>css_class</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[0]['css_class']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>icon</dfn> =&gt; <var>string</var> (11) \"fas fa-home\"<div class=\"access-path\">$value[0]['icon']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sort_order</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['sort_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[0]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-20 09:44:06\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-20 09:44:06\"<div class=\"access-path\">$value[0]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>page_title</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['page_title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>page_slug</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['page_slug']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>children</dfn> =&gt; <var>array</var> (0)<div class=\"access-path\">$value[0]['children']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[1]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>menu_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[1]['menu_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>parent_id</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['parent_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (5) \"About\"<div class=\"access-path\">$value[1]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>url</dfn> =&gt; <var>string</var> (11) \"/page/about\"<div class=\"access-path\">$value[1]['url']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>page_id</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['page_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>type</dfn> =&gt; <var>string</var> (6) \"custom\"<div class=\"access-path\">$value[1]['type']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>target</dfn> =&gt; <var>string</var> (5) \"_self\"<div class=\"access-path\">$value[1]['target']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>css_class</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['css_class']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>icon</dfn> =&gt; <var>string</var> (18) \"fas fa-info-circle\"<div class=\"access-path\">$value[1]['icon']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sort_order</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[1]['sort_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[1]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-20 09:44:06\"<div class=\"access-path\">$value[1]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-20 09:44:06\"<div class=\"access-path\">$value[1]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>page_title</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['page_title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>page_slug</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['page_slug']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>children</dfn> =&gt; <var>array</var> (0)<div class=\"access-path\">$value[1]['children']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[2]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>menu_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[2]['menu_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>parent_id</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['parent_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (4) \"Blog\"<div class=\"access-path\">$value[2]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>url</dfn> =&gt; <var>string</var> (5) \"/blog\"<div class=\"access-path\">$value[2]['url']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>page_id</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['page_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>type</dfn> =&gt; <var>string</var> (6) \"custom\"<div class=\"access-path\">$value[2]['type']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>target</dfn> =&gt; <var>string</var> (5) \"_self\"<div class=\"access-path\">$value[2]['target']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>css_class</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]['css_class']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>icon</dfn> =&gt; <var>string</var> (11) \"fas fa-blog\"<div class=\"access-path\">$value[2]['icon']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sort_order</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[2]['sort_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[2]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-20 09:44:06\"<div class=\"access-path\">$value[2]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-20 09:44:06\"<div class=\"access-path\">$value[2]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>page_title</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['page_title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>page_slug</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['page_slug']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>children</dfn> =&gt; <var>array</var> (0)<div class=\"access-path\">$value[2]['children']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>3</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[3]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[3]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>menu_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[3]['menu_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>parent_id</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['parent_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (8) \"Services\"<div class=\"access-path\">$value[3]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>url</dfn> =&gt; <var>string</var> (1) \"#\"<div class=\"access-path\">$value[3]['url']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>page_id</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['page_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>type</dfn> =&gt; <var>string</var> (6) \"custom\"<div class=\"access-path\">$value[3]['type']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>target</dfn> =&gt; <var>string</var> (5) \"_self\"<div class=\"access-path\">$value[3]['target']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>css_class</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['css_class']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>icon</dfn> =&gt; <var>string</var> (11) \"fas fa-cogs\"<div class=\"access-path\">$value[3]['icon']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sort_order</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[3]['sort_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[3]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-20 09:44:06\"<div class=\"access-path\">$value[3]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-20 09:44:06\"<div class=\"access-path\">$value[3]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>page_title</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['page_title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>page_slug</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['page_slug']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>children</dfn> =&gt; <var>array</var> (3)<div class=\"access-path\">$value[3]['children']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (3)</li><li>Contents (3)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>id</th><th>menu_id</th><th>parent_id</th><th>title</th><th>url</th><th>page_id</th><th>type</th><th>target</th><th>css_class</th><th>icon</th><th>sort_order</th><th>status</th><th>created_at</th><th>updated_at</th><th>page_title</th><th>page_slug</th><th>children</th></tr></thead><tbody><tr><th>0</th><td title=\"string (1)\">6</td><td title=\"string (1)\">1</td><td title=\"string (1)\">4</td><td title=\"string (15)\">Web Development</td><td title=\"string (21)\">/page/web-development</td><td title=\"null\"><var>null</var></td><td title=\"string (6)\">custom</td><td title=\"string (5)\">_self</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (1)\">1</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-06-20 09:44:06</td><td title=\"string (19)\">2025-06-20 09:44:06</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"array (0)\"><var>array</var> (0)</td></tr><tr><th>1</th><td title=\"string (1)\">7</td><td title=\"string (1)\">1</td><td title=\"string (1)\">4</td><td title=\"string (6)\">Design</td><td title=\"string (12)\">/page/design</td><td title=\"null\"><var>null</var></td><td title=\"string (6)\">custom</td><td title=\"string (5)\">_self</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (1)\">2</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-06-20 09:44:06</td><td title=\"string (19)\">2025-06-20 09:44:06</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"array (0)\"><var>array</var> (0)</td></tr><tr><th>2</th><td title=\"string (1)\">8</td><td title=\"string (1)\">1</td><td title=\"string (1)\">4</td><td title=\"string (10)\">Consulting</td><td title=\"string (16)\">/page/consulting</td><td title=\"null\"><var>null</var></td><td title=\"string (6)\">custom</td><td title=\"string (5)\">_self</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (1)\">3</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-06-20 09:44:06</td><td title=\"string (19)\">2025-06-20 09:44:06</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"array (0)\"><var>array</var> (0)</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[3]['children'][0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"6\"<div class=\"access-path\">$value[3]['children'][0]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>menu_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[3]['children'][0]['menu_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>parent_id</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[3]['children'][0]['parent_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (15) \"Web Development\"<div class=\"access-path\">$value[3]['children'][0]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>url</dfn> =&gt; <var>string</var> (21) \"/page/web-development\"<div class=\"access-path\">$value[3]['children'][0]['url']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>page_id</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['children'][0]['page_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>type</dfn> =&gt; <var>string</var> (6) \"custom\"<div class=\"access-path\">$value[3]['children'][0]['type']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>target</dfn> =&gt; <var>string</var> (5) \"_self\"<div class=\"access-path\">$value[3]['children'][0]['target']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>css_class</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['children'][0]['css_class']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>icon</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['children'][0]['icon']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sort_order</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[3]['children'][0]['sort_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[3]['children'][0]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-20 09:44:06\"<div class=\"access-path\">$value[3]['children'][0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-20 09:44:06\"<div class=\"access-path\">$value[3]['children'][0]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>page_title</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['children'][0]['page_title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>page_slug</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['children'][0]['page_slug']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>children</dfn> =&gt; <var>array</var> (0)<div class=\"access-path\">$value[3]['children'][0]['children']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[3]['children'][1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"7\"<div class=\"access-path\">$value[3]['children'][1]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>menu_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[3]['children'][1]['menu_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>parent_id</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[3]['children'][1]['parent_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (6) \"Design\"<div class=\"access-path\">$value[3]['children'][1]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>url</dfn> =&gt; <var>string</var> (12) \"/page/design\"<div class=\"access-path\">$value[3]['children'][1]['url']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>page_id</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['children'][1]['page_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>type</dfn> =&gt; <var>string</var> (6) \"custom\"<div class=\"access-path\">$value[3]['children'][1]['type']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>target</dfn> =&gt; <var>string</var> (5) \"_self\"<div class=\"access-path\">$value[3]['children'][1]['target']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>css_class</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['children'][1]['css_class']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>icon</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['children'][1]['icon']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sort_order</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[3]['children'][1]['sort_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[3]['children'][1]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-20 09:44:06\"<div class=\"access-path\">$value[3]['children'][1]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-20 09:44:06\"<div class=\"access-path\">$value[3]['children'][1]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>page_title</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['children'][1]['page_title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>page_slug</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['children'][1]['page_slug']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>children</dfn> =&gt; <var>array</var> (0)<div class=\"access-path\">$value[3]['children'][1]['children']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[3]['children'][2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"8\"<div class=\"access-path\">$value[3]['children'][2]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>menu_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[3]['children'][2]['menu_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>parent_id</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[3]['children'][2]['parent_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (10) \"Consulting\"<div class=\"access-path\">$value[3]['children'][2]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>url</dfn> =&gt; <var>string</var> (16) \"/page/consulting\"<div class=\"access-path\">$value[3]['children'][2]['url']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>page_id</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['children'][2]['page_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>type</dfn> =&gt; <var>string</var> (6) \"custom\"<div class=\"access-path\">$value[3]['children'][2]['type']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>target</dfn> =&gt; <var>string</var> (5) \"_self\"<div class=\"access-path\">$value[3]['children'][2]['target']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>css_class</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['children'][2]['css_class']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>icon</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['children'][2]['icon']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sort_order</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[3]['children'][2]['sort_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[3]['children'][2]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-20 09:44:06\"<div class=\"access-path\">$value[3]['children'][2]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-20 09:44:06\"<div class=\"access-path\">$value[3]['children'][2]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>page_title</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['children'][2]['page_title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>page_slug</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['children'][2]['page_slug']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>children</dfn> =&gt; <var>array</var> (0)<div class=\"access-path\">$value[3]['children'][2]['children']</div></dt></dl></dd></dl></li></ul></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>4</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[4]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"5\"<div class=\"access-path\">$value[4]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>menu_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[4]['menu_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>parent_id</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['parent_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (7) \"Contact\"<div class=\"access-path\">$value[4]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>url</dfn> =&gt; <var>string</var> (13) \"/page/contact\"<div class=\"access-path\">$value[4]['url']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>page_id</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['page_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>type</dfn> =&gt; <var>string</var> (6) \"custom\"<div class=\"access-path\">$value[4]['type']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>target</dfn> =&gt; <var>string</var> (5) \"_self\"<div class=\"access-path\">$value[4]['target']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>css_class</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[4]['css_class']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>icon</dfn> =&gt; <var>string</var> (15) \"fas fa-envelope\"<div class=\"access-path\">$value[4]['icon']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sort_order</dfn> =&gt; <var>string</var> (1) \"5\"<div class=\"access-path\">$value[4]['sort_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[4]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-20 09:44:06\"<div class=\"access-path\">$value[4]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-20 09:44:06\"<div class=\"access-path\">$value[4]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>page_title</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['page_title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>page_slug</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['page_slug']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>children</dfn> =&gt; <var>array</var> (0)<div class=\"access-path\">$value[4]['children']</div></dt></dl></dd></dl></li></ul></dd></dl></div>", "footer_menu_items": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (3)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (3)</li><li>Contents (3)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>id</th><th>menu_id</th><th>parent_id</th><th>title</th><th>url</th><th>page_id</th><th>type</th><th>target</th><th>css_class</th><th>icon</th><th>sort_order</th><th>status</th><th>created_at</th><th>updated_at</th><th>page_title</th><th>page_slug</th><th>children</th></tr></thead><tbody><tr><th>0</th><td title=\"string (1)\">9</td><td title=\"string (1)\">2</td><td title=\"null\"><var>null</var></td><td title=\"string (14)\">Privacy Policy</td><td title=\"string (20)\">/page/privacy-policy</td><td title=\"null\"><var>null</var></td><td title=\"string (6)\">custom</td><td title=\"string (5)\">_self</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (1)\">1</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-06-20 09:44:06</td><td title=\"string (19)\">2025-06-20 09:44:06</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"array (0)\"><var>array</var> (0)</td></tr><tr><th>1</th><td title=\"string (2)\">10</td><td title=\"string (1)\">2</td><td title=\"null\"><var>null</var></td><td title=\"string (16)\">Terms of Service</td><td title=\"string (22)\">/page/terms-of-service</td><td title=\"null\"><var>null</var></td><td title=\"string (6)\">custom</td><td title=\"string (5)\">_self</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (1)\">2</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-06-20 09:44:06</td><td title=\"string (19)\">2025-06-20 09:44:06</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"array (0)\"><var>array</var> (0)</td></tr><tr><th>2</th><td title=\"string (2)\">11</td><td title=\"string (1)\">2</td><td title=\"null\"><var>null</var></td><td title=\"string (7)\">Support</td><td title=\"string (13)\">/page/support</td><td title=\"null\"><var>null</var></td><td title=\"string (6)\">custom</td><td title=\"string (5)\">_self</td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (1)\">3</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-06-20 09:44:06</td><td title=\"string (19)\">2025-06-20 09:44:06</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"array (0)\"><var>array</var> (0)</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"9\"<div class=\"access-path\">$value[0]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>menu_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[0]['menu_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>parent_id</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['parent_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (14) \"Privacy Policy\"<div class=\"access-path\">$value[0]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>url</dfn> =&gt; <var>string</var> (20) \"/page/privacy-policy\"<div class=\"access-path\">$value[0]['url']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>page_id</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['page_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>type</dfn> =&gt; <var>string</var> (6) \"custom\"<div class=\"access-path\">$value[0]['type']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>target</dfn> =&gt; <var>string</var> (5) \"_self\"<div class=\"access-path\">$value[0]['target']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>css_class</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[0]['css_class']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>icon</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[0]['icon']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sort_order</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['sort_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[0]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-20 09:44:06\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-20 09:44:06\"<div class=\"access-path\">$value[0]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>page_title</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['page_title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>page_slug</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['page_slug']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>children</dfn> =&gt; <var>array</var> (0)<div class=\"access-path\">$value[0]['children']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"10\"<div class=\"access-path\">$value[1]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>menu_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[1]['menu_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>parent_id</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['parent_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (16) \"Terms of Service\"<div class=\"access-path\">$value[1]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>url</dfn> =&gt; <var>string</var> (22) \"/page/terms-of-service\"<div class=\"access-path\">$value[1]['url']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>page_id</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['page_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>type</dfn> =&gt; <var>string</var> (6) \"custom\"<div class=\"access-path\">$value[1]['type']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>target</dfn> =&gt; <var>string</var> (5) \"_self\"<div class=\"access-path\">$value[1]['target']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>css_class</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['css_class']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>icon</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['icon']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sort_order</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[1]['sort_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[1]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-20 09:44:06\"<div class=\"access-path\">$value[1]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-20 09:44:06\"<div class=\"access-path\">$value[1]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>page_title</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['page_title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>page_slug</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['page_slug']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>children</dfn> =&gt; <var>array</var> (0)<div class=\"access-path\">$value[1]['children']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (17)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"11\"<div class=\"access-path\">$value[2]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>menu_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[2]['menu_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>parent_id</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['parent_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (7) \"Support\"<div class=\"access-path\">$value[2]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>url</dfn> =&gt; <var>string</var> (13) \"/page/support\"<div class=\"access-path\">$value[2]['url']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>page_id</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['page_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>type</dfn> =&gt; <var>string</var> (6) \"custom\"<div class=\"access-path\">$value[2]['type']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>target</dfn> =&gt; <var>string</var> (5) \"_self\"<div class=\"access-path\">$value[2]['target']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>css_class</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]['css_class']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>icon</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]['icon']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>sort_order</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[2]['sort_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[2]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-20 09:44:06\"<div class=\"access-path\">$value[2]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-20 09:44:06\"<div class=\"access-path\">$value[2]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>page_title</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['page_title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>page_slug</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['page_slug']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>children</dfn> =&gt; <var>array</var> (0)<div class=\"access-path\">$value[2]['children']</div></dt></dl></dd></dl></li></ul></dd></dl></div>", "google_analytics_id": "<div class=\"kint-rich\"><dl><dt><dfn>$value</dfn> <var>null</var></dt></dl></div>"}}, "headers": {"Host": "localhost:8081", "Connection": "keep-alive", "User-Agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Accept": "*/*", "Sec-Gpc": "1", "Accept-Language": "en-US,en;q=0.8", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://localhost:8081/sw.js", "Accept-Encoding": "gzip, deflate, br, zstd", "Cookie": "admin_auth=eyJpdiI6IkQ3T3RUQngyc043Q3RZSWVnSjhWaFE9PSIsInZhbHVlIjoiRTlGaDVKSTMxdm15ODBTM2tvZFdQRldxL3RNNnY4UXJBc3RDWGU3SUJMVFhKYnJ1OTM4NzZLb0FWVzBRTDdZTy9abzg1MXJuWTNBaW0wNElrMDd2NUtheFNvTlI4cWc4NlhVa3VoaXBHOTJJRDgybGQ2WXp1NVkxODBFVTliVzYyQ2Q5bEo4bVNrbjhDaVBmbDNNNE1BPT0iLCJtYWMiOiJmNzIxZjQzOWQ0ZDJiOGUxNTU3ODZlZmI3YzllYmI3MGRhNjQ3YWIxZTc2NGUyNTMzOTg4NjA1YmIyZGRiYzAwIiwidGFnIjoiIn0%3D; ci_session=b8dfda45285fee043d825acdfffd197f"}, "cookies": {"admin_auth": "eyJpdiI6IkQ3T3RUQngyc043Q3RZSWVnSjhWaFE9PSIsInZhbHVlIjoiRTlGaDVKSTMxdm15ODBTM2tvZFdQRldxL3RNNnY4UXJBc3RDWGU3SUJMVFhKYnJ1OTM4NzZLb0FWVzBRTDdZTy9abzg1MXJuWTNBaW0wNElrMDd2NUtheFNvTlI4cWc4NlhVa3VoaXBHOTJJRDgybGQ2WXp1NVkxODBFVTliVzYyQ2Q5bEo4bVNrbjhDaVBmbDNNNE1BPT0iLCJtYWMiOiJmNzIxZjQzOWQ0ZDJiOGUxNTU3ODZlZmI3YzllYmI3MGRhNjQ3YWIxZTc2NGUyNTMzOTg4NjA1YmIyZGRiYzAwIiwidGFnIjoiIn0=", "ci_session": "b8dfda45285fee043d825acdfffd197f"}, "request": "HTTP/1.1", "response": {"statusCode": 200, "reason": "OK", "contentType": "text/html; charset=UTF-8", "headers": {"Cache-Control": "no-store, max-age=0, no-cache", "Content-Type": "text/html; charset=UTF-8"}}}, "config": {"ciVersion": "4.6.1", "phpVersion": "8.2.12", "phpSAPI": "cli-server", "environment": "development", "baseURL": "http://localhost:8081/", "timezone": "UTC", "locale": "en", "cspEnabled": false}}