{"url": "http://localhost:8081/admin/themes/create", "method": "GET", "isAJAX": false, "startTime": **********.595811, "totalTime": 107.6, "totalMemory": "7.169", "segmentDuration": 20, "segmentCount": 6, "CI_VERSION": "4.6.1", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.600109, "duration": 0.*****************}, {"name": "Required Before Filters", "component": "Timer", "start": **********.634188, "duration": 0.003779172897338867}, {"name": "Routing", "component": "Timer", "start": **********.637978, "duration": 0.0023949146270751953}, {"name": "Before Filters", "component": "Timer", "start": **********.64068, "duration": 0.045796871185302734}, {"name": "Controller", "component": "Timer", "start": **********.686483, "duration": 0.016225099563598633}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.686485, "duration": 0.006855964660644531}, {"name": "After Filters", "component": "Timer", "start": **********.702728, "duration": 1.6927719116210938e-05}, {"name": "Required After Filters", "component": "Timer", "start": **********.702799, "duration": 0.0006709098815917969}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(1 total Query, 1  unique across 1 Connection)", "display": {"queries": [{"hover": "", "class": "", "duration": "1 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `users`\n<strong>WHERE</strong> `users`.`id` = 1", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:210", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:613", "function": "        CodeIgniter\\Model->doFind()", "index": "  3    "}, {"file": "APPPATH/Models/UserModel.php:94", "function": "        CodeIgniter\\BaseModel->find()", "index": "  4    "}, {"file": "APPPATH/Filters/PermissionFilter.php:42", "function": "        App\\Models\\UserModel->hasPermission()", "index": "  5    "}, {"file": "SYSTEMPATH/Filters/Filters.php:241", "function": "        App\\Filters\\PermissionFilter->before()", "index": "  6    "}, {"file": "SYSTEMPATH/Filters/Filters.php:221", "function": "        CodeIgniter\\Filters\\Filters->runBefore()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:479", "function": "        CodeIgniter\\Filters\\Filters->run()", "index": "  8    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH/index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}, {"file": "SYSTEMPATH/rewrite.php:44", "args": ["/opt/lampp/htdocs/cms/public/index.php"], "function": "        require_once()", "index": " 13    "}], "trace-file": "APPPATH/Models/UserModel.php:94", "qid": "667f2db76bdf492c3e9aa2abf260ad8d"}]}, "badgeValue": 1, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": **********.672227, "duration": "0.009702"}, {"name": "Query", "component": "Database", "start": **********.683291, "duration": "0.001005", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `users`\n<strong>WHERE</strong> `users`.`id` = 1"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": [{"level": "debug", "msg": "Session: Class initialized using 'CodeIgniter\\Session\\Handlers\\FileHandler' driver."}]}, "badgeValue": null, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": [], "badgeValue": 2, "isEmpty": false, "hasTabContent": false, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "View: admin/layouts/main.php", "component": "Views", "start": **********.700733, "duration": 0.001898050308227539}, {"name": "View: admin/themes/create.php", "component": "Views", "start": **********.696394, "duration": 0.006287097930908203}]}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 185 )", "display": {"coreFiles": [{"path": "SYSTEMPATH/API/ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH/Autoloader/Autoloader.php", "name": "Autoloader.php"}, {"path": "SYSTEMPATH/Autoloader/FileLocator.php", "name": "FileLocator.php"}, {"path": "SYSTEMPATH/Autoloader/FileLocatorInterface.php", "name": "FileLocatorInterface.php"}, {"path": "SYSTEMPATH/BaseModel.php", "name": "BaseModel.php"}, {"path": "SYSTEMPATH/Boot.php", "name": "Boot.php"}, {"path": "SYSTEMPATH/Cache/CacheFactory.php", "name": "CacheFactory.php"}, {"path": "SYSTEMPATH/Cache/CacheInterface.php", "name": "CacheInterface.php"}, {"path": "SYSTEMPATH/Cache/Handlers/BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH/Cache/Handlers/FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH/Cache/ResponseCache.php", "name": "ResponseCache.php"}, {"path": "SYSTEMPATH/CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "SYSTEMPATH/Common.php", "name": "Common.php"}, {"path": "SYSTEMPATH/Config/AutoloadConfig.php", "name": "AutoloadConfig.php"}, {"path": "SYSTEMPATH/Config/BaseConfig.php", "name": "BaseConfig.php"}, {"path": "SYSTEMPATH/Config/BaseService.php", "name": "BaseService.php"}, {"path": "SYSTEMPATH/Config/DotEnv.php", "name": "DotEnv.php"}, {"path": "SYSTEMPATH/Config/Factories.php", "name": "Factories.php"}, {"path": "SYSTEMPATH/Config/Factory.php", "name": "Factory.php"}, {"path": "SYSTEMPATH/Config/Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH/Config/Routing.php", "name": "Routing.php"}, {"path": "SYSTEMPATH/Config/Services.php", "name": "Services.php"}, {"path": "SYSTEMPATH/Config/View.php", "name": "View.php"}, {"path": "SYSTEMPATH/Controller.php", "name": "Controller.php"}, {"path": "SYSTEMPATH/Cookie/CloneableCookieInterface.php", "name": "CloneableCookieInterface.php"}, {"path": "SYSTEMPATH/Cookie/Cookie.php", "name": "Cookie.php"}, {"path": "SYSTEMPATH/Cookie/CookieInterface.php", "name": "CookieInterface.php"}, {"path": "SYSTEMPATH/Cookie/CookieStore.php", "name": "CookieStore.php"}, {"path": "SYSTEMPATH/DataCaster/Cast/ArrayCast.php", "name": "ArrayCast.php"}, {"path": "SYSTEMPATH/DataCaster/Cast/BaseCast.php", "name": "BaseCast.php"}, {"path": "SYSTEMPATH/DataCaster/Cast/BooleanCast.php", "name": "BooleanCast.php"}, {"path": "SYSTEMPATH/DataCaster/Cast/CSVCast.php", "name": "CSVCast.php"}, {"path": "SYSTEMPATH/DataCaster/Cast/CastInterface.php", "name": "CastInterface.php"}, {"path": "SYSTEMPATH/DataCaster/Cast/DatetimeCast.php", "name": "DatetimeCast.php"}, {"path": "SYSTEMPATH/DataCaster/Cast/FloatCast.php", "name": "FloatCast.php"}, {"path": "SYSTEMPATH/DataCaster/Cast/IntBoolCast.php", "name": "IntBoolCast.php"}, {"path": "SYSTEMPATH/DataCaster/Cast/IntegerCast.php", "name": "IntegerCast.php"}, {"path": "SYSTEMPATH/DataCaster/Cast/JsonCast.php", "name": "JsonCast.php"}, {"path": "SYSTEMPATH/DataCaster/Cast/TimestampCast.php", "name": "TimestampCast.php"}, {"path": "SYSTEMPATH/DataCaster/Cast/URICast.php", "name": "URICast.php"}, {"path": "SYSTEMPATH/DataCaster/DataCaster.php", "name": "DataCaster.php"}, {"path": "SYSTEMPATH/DataConverter/DataConverter.php", "name": "DataConverter.php"}, {"path": "SYSTEMPATH/Database/BaseBuilder.php", "name": "BaseBuilder.php"}, {"path": "SYSTEMPATH/Database/BaseConnection.php", "name": "BaseConnection.php"}, {"path": "SYSTEMPATH/Database/BaseResult.php", "name": "BaseResult.php"}, {"path": "SYSTEMPATH/Database/Config.php", "name": "Config.php"}, {"path": "SYSTEMPATH/Database/ConnectionInterface.php", "name": "ConnectionInterface.php"}, {"path": "SYSTEMPATH/Database/Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH/Database/MySQLi/Builder.php", "name": "Builder.php"}, {"path": "SYSTEMPATH/Database/MySQLi/Connection.php", "name": "Connection.php"}, {"path": "SYSTEMPATH/Database/MySQLi/Result.php", "name": "Result.php"}, {"path": "SYSTEMPATH/Database/Query.php", "name": "Query.php"}, {"path": "SYSTEMPATH/Database/QueryInterface.php", "name": "QueryInterface.php"}, {"path": "SYSTEMPATH/Database/ResultInterface.php", "name": "ResultInterface.php"}, {"path": "SYSTEMPATH/Debug/Exceptions.php", "name": "Exceptions.php"}, {"path": "SYSTEMPATH/Debug/Timer.php", "name": "Timer.php"}, {"path": "SYSTEMPATH/Debug/Toolbar.php", "name": "Toolbar.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/BaseCollector.php", "name": "BaseCollector.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Files.php", "name": "Files.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Logs.php", "name": "Logs.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Routes.php", "name": "Routes.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Timers.php", "name": "Timers.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Views.php", "name": "Views.php"}, {"path": "SYSTEMPATH/Events/Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH/Filters/DebugToolbar.php", "name": "DebugToolbar.php"}, {"path": "SYSTEMPATH/Filters/FilterInterface.php", "name": "FilterInterface.php"}, {"path": "SYSTEMPATH/Filters/Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH/Filters/ForceHTTPS.php", "name": "ForceHTTPS.php"}, {"path": "SYSTEMPATH/Filters/PageCache.php", "name": "PageCache.php"}, {"path": "SYSTEMPATH/Filters/PerformanceMetrics.php", "name": "PerformanceMetrics.php"}, {"path": "SYSTEMPATH/HTTP/ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "SYSTEMPATH/HTTP/Header.php", "name": "Header.php"}, {"path": "SYSTEMPATH/HTTP/IncomingRequest.php", "name": "IncomingRequest.php"}, {"path": "SYSTEMPATH/HTTP/Message.php", "name": "Message.php"}, {"path": "SYSTEMPATH/HTTP/MessageInterface.php", "name": "MessageInterface.php"}, {"path": "SYSTEMPATH/HTTP/MessageTrait.php", "name": "MessageTrait.php"}, {"path": "SYSTEMPATH/HTTP/Method.php", "name": "Method.php"}, {"path": "SYSTEMPATH/HTTP/OutgoingRequest.php", "name": "OutgoingRequest.php"}, {"path": "SYSTEMPATH/HTTP/OutgoingRequestInterface.php", "name": "OutgoingRequestInterface.php"}, {"path": "SYSTEMPATH/HTTP/Request.php", "name": "Request.php"}, {"path": "SYSTEMPATH/HTTP/RequestInterface.php", "name": "RequestInterface.php"}, {"path": "SYSTEMPATH/HTTP/RequestTrait.php", "name": "RequestTrait.php"}, {"path": "SYSTEMPATH/HTTP/Response.php", "name": "Response.php"}, {"path": "SYSTEMPATH/HTTP/ResponseInterface.php", "name": "ResponseInterface.php"}, {"path": "SYSTEMPATH/HTTP/ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH/HTTP/SiteURI.php", "name": "SiteURI.php"}, {"path": "SYSTEMPATH/HTTP/SiteURIFactory.php", "name": "SiteURIFactory.php"}, {"path": "SYSTEMPATH/HTTP/URI.php", "name": "URI.php"}, {"path": "SYSTEMPATH/HTTP/UserAgent.php", "name": "UserAgent.php"}, {"path": "SYSTEMPATH/Helpers/Array/ArrayHelper.php", "name": "ArrayHelper.php"}, {"path": "SYSTEMPATH/Helpers/array_helper.php", "name": "array_helper.php"}, {"path": "SYSTEMPATH/Helpers/filesystem_helper.php", "name": "filesystem_helper.php"}, {"path": "SYSTEMPATH/Helpers/kint_helper.php", "name": "kint_helper.php"}, {"path": "SYSTEMPATH/Helpers/url_helper.php", "name": "url_helper.php"}, {"path": "SYSTEMPATH/I18n/Time.php", "name": "Time.php"}, {"path": "SYSTEMPATH/I18n/TimeTrait.php", "name": "TimeTrait.php"}, {"path": "SYSTEMPATH/Log/Handlers/BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH/Log/Handlers/FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH/Log/Handlers/HandlerInterface.php", "name": "HandlerInterface.php"}, {"path": "SYSTEMPATH/Log/Logger.php", "name": "Logger.php"}, {"path": "SYSTEMPATH/Model.php", "name": "Model.php"}, {"path": "SYSTEMPATH/Modules/Modules.php", "name": "Modules.php"}, {"path": "SYSTEMPATH/Router/RouteCollection.php", "name": "RouteCollection.php"}, {"path": "SYSTEMPATH/Router/RouteCollectionInterface.php", "name": "RouteCollectionInterface.php"}, {"path": "SYSTEMPATH/Router/Router.php", "name": "Router.php"}, {"path": "SYSTEMPATH/Router/RouterInterface.php", "name": "RouterInterface.php"}, {"path": "SYSTEMPATH/Security/Security.php", "name": "Security.php"}, {"path": "SYSTEMPATH/Security/SecurityInterface.php", "name": "SecurityInterface.php"}, {"path": "SYSTEMPATH/Session/Handlers/BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH/Session/Handlers/FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH/Session/Session.php", "name": "Session.php"}, {"path": "SYSTEMPATH/Session/SessionInterface.php", "name": "SessionInterface.php"}, {"path": "SYSTEMPATH/Superglobals.php", "name": "Superglobals.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/FacadeInterface.php", "name": "FacadeInterface.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Kint.php", "name": "Kint.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/AbstractRenderer.php", "name": "AbstractRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/AssetRendererTrait.php", "name": "AssetRendererTrait.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/CliRenderer.php", "name": "CliRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/ConstructableRendererInterface.php", "name": "ConstructableRendererInterface.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/RichRenderer.php", "name": "RichRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/TextRenderer.php", "name": "TextRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Utils.php", "name": "Utils.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/init.php", "name": "init.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/init_helpers.php", "name": "init_helpers.php"}, {"path": "SYSTEMPATH/Traits/ConditionalTrait.php", "name": "ConditionalTrait.php"}, {"path": "SYSTEMPATH/Validation/FormatRules.php", "name": "FormatRules.php"}, {"path": "SYSTEMPATH/View/RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH/View/View.php", "name": "View.php"}, {"path": "SYSTEMPATH/View/ViewDecoratorTrait.php", "name": "ViewDecoratorTrait.php"}, {"path": "SYSTEMPATH/rewrite.php", "name": "rewrite.php"}], "userFiles": [{"path": "APPPATH/Common.php", "name": "Common.php"}, {"path": "APPPATH/Config/App.php", "name": "App.php"}, {"path": "APPPATH/Config/Autoload.php", "name": "Autoload.php"}, {"path": "APPPATH/Config/Boot/development.php", "name": "development.php"}, {"path": "APPPATH/Config/Cache.php", "name": "Cache.php"}, {"path": "APPPATH/Config/Constants.php", "name": "Constants.php"}, {"path": "APPPATH/Config/ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "APPPATH/Config/Cookie.php", "name": "Cookie.php"}, {"path": "APPPATH/Config/Database.php", "name": "Database.php"}, {"path": "APPPATH/Config/DocTypes.php", "name": "DocTypes.php"}, {"path": "APPPATH/Config/Events.php", "name": "Events.php"}, {"path": "APPPATH/Config/Exceptions.php", "name": "Exceptions.php"}, {"path": "APPPATH/Config/Feature.php", "name": "Feature.php"}, {"path": "APPPATH/Config/Filters.php", "name": "Filters.php"}, {"path": "APPPATH/Config/Kint.php", "name": "Kint.php"}, {"path": "APPPATH/Config/Logger.php", "name": "Logger.php"}, {"path": "APPPATH/Config/Modules.php", "name": "Modules.php"}, {"path": "APPPATH/Config/Optimize.php", "name": "Optimize.php"}, {"path": "APPPATH/Config/Paths.php", "name": "Paths.php"}, {"path": "APPPATH/Config/Routes.php", "name": "Routes.php"}, {"path": "APPPATH/Config/Routing.php", "name": "Routing.php"}, {"path": "APPPATH/Config/Security.php", "name": "Security.php"}, {"path": "APPPATH/Config/Services.php", "name": "Services.php"}, {"path": "APPPATH/Config/Session.php", "name": "Session.php"}, {"path": "APPPATH/Config/Toolbar.php", "name": "Toolbar.php"}, {"path": "APPPATH/Config/UserAgents.php", "name": "UserAgents.php"}, {"path": "APPPATH/Config/View.php", "name": "View.php"}, {"path": "APPPATH/Controllers/Admin/ThemeController.php", "name": "ThemeController.php"}, {"path": "APPPATH/Controllers/BaseController.php", "name": "BaseController.php"}, {"path": "APPPATH/Filters/AuthFilter.php", "name": "AuthFilter.php"}, {"path": "APPPATH/Filters/PermissionFilter.php", "name": "PermissionFilter.php"}, {"path": "APPPATH/Models/SettingsModel.php", "name": "SettingsModel.php"}, {"path": "APPPATH/Models/TemplateModel.php", "name": "TemplateModel.php"}, {"path": "APPPATH/Models/UserModel.php", "name": "UserModel.php"}, {"path": "APPPATH/Views/admin/layouts/main.php", "name": "main.php"}, {"path": "APPPATH/Views/admin/themes/create.php", "name": "create.php"}, {"path": "FCPATH/index.php", "name": "index.php"}, {"path": "VENDORPATH/autoload.php", "name": "autoload.php"}, {"path": "VENDORPATH/composer/ClassLoader.php", "name": "ClassLoader.php"}, {"path": "VENDORPATH/composer/InstalledVersions.php", "name": "InstalledVersions.php"}, {"path": "VENDORPATH/composer/autoload_real.php", "name": "autoload_real.php"}, {"path": "VENDORPATH/composer/autoload_static.php", "name": "autoload_static.php"}, {"path": "VENDORPATH/composer/installed.php", "name": "installed.php"}, {"path": "VENDORPATH/composer/platform_check.php", "name": "platform_check.php"}, {"path": "VENDORPATH/laminas/laminas-escaper/src/Escaper.php", "name": "Escaper.php"}, {"path": "VENDORPATH/laminas/laminas-escaper/src/EscaperInterface.php", "name": "EscaperInterface.php"}, {"path": "VENDORPATH/myclabs/deep-copy/src/DeepCopy/deep_copy.php", "name": "deep_copy.php"}, {"path": "VENDORPATH/phpunit/phpunit/src/Framework/Assert/Functions.php", "name": "Functions.php"}, {"path": "VENDORPATH/psr/log/src/LogLevel.php", "name": "LogLevel.php"}, {"path": "VENDORPATH/psr/log/src/LoggerAwareTrait.php", "name": "LoggerAwareTrait.php"}, {"path": "VENDORPATH/psr/log/src/LoggerInterface.php", "name": "LoggerInterface.php"}, {"path": "VENDORPATH/symfony/deprecation-contracts/function.php", "name": "function.php"}]}, "badgeValue": 185, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"directory": "", "controller": "\\App\\Controllers\\Admin\\ThemeController", "method": "create", "paramCount": 0, "truePCount": 0, "params": []}], "routes": [{"method": "GET", "route": "/", "handler": "\\App\\Controllers\\Home::index"}, {"method": "GET", "route": "page/([^/]+)", "handler": "\\App\\Controllers\\Home::page/$1"}, {"method": "GET", "route": "blog", "handler": "\\App\\Controllers\\BlogController::index"}, {"method": "GET", "route": "blog/page/([0-9]+)", "handler": "\\App\\Controllers\\BlogController::index/$1"}, {"method": "GET", "route": "blog/([^/]+)", "handler": "\\App\\Controllers\\BlogController::post/$1"}, {"method": "GET", "route": "category/([^/]+)", "handler": "\\App\\Controllers\\BlogController::category/$1"}, {"method": "GET", "route": "category/([^/]+)/page/([0-9]+)", "handler": "\\App\\Controllers\\BlogController::category/$1/$2"}, {"method": "GET", "route": "tag/([^/]+)", "handler": "\\App\\Controllers\\BlogController::tag/$1"}, {"method": "GET", "route": "tag/([^/]+)/page/([0-9]+)", "handler": "\\App\\Controllers\\BlogController::tag/$1/$2"}, {"method": "GET", "route": "sitemap.xml", "handler": "\\App\\Controllers\\SitemapController::index"}, {"method": "GET", "route": "robots.txt", "handler": "\\App\\Controllers\\SitemapController::robots"}, {"method": "GET", "route": "rss.xml", "handler": "\\App\\Controllers\\SitemapController::rss"}, {"method": "GET", "route": "feed.json", "handler": "\\App\\Controllers\\SitemapController::jsonFeed"}, {"method": "GET", "route": "manifest.json", "handler": "\\App\\Controllers\\SitemapController::manifest"}, {"method": "GET", "route": "auth/login", "handler": "\\App\\Controllers\\AuthController::login"}, {"method": "GET", "route": "auth/register", "handler": "\\App\\Controllers\\AuthController::register"}, {"method": "GET", "route": "auth/logout", "handler": "\\App\\Controllers\\AuthController::logout"}, {"method": "GET", "route": "auth/check", "handler": "\\App\\Controllers\\AuthController::checkAuth"}, {"method": "GET", "route": "user/dashboard", "handler": "\\App\\Controllers\\UserDashboardController::index"}, {"method": "GET", "route": "user/profile", "handler": "\\App\\Controllers\\UserDashboardController::profile"}, {"method": "GET", "route": "user/posts", "handler": "\\App\\Controllers\\UserDashboardController::myPosts"}, {"method": "GET", "route": "user/pages", "handler": "\\App\\Controllers\\UserDashboardController::myPages"}, {"method": "GET", "route": "user/media", "handler": "\\App\\Controllers\\UserDashboardController::myMedia"}, {"method": "GET", "route": "user/settings", "handler": "\\App\\Controllers\\UserDashboardController::settings"}, {"method": "GET", "route": "admin/dashboard", "handler": "\\App\\Controllers\\Admin\\DashboardController::index"}, {"method": "GET", "route": "admin/pages", "handler": "\\App\\Controllers\\Admin\\PageController::index"}, {"method": "GET", "route": "admin/pages/create", "handler": "\\App\\Controllers\\Admin\\PageController::create"}, {"method": "GET", "route": "admin/pages/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\PageController::edit/$1"}, {"method": "GET", "route": "admin/pages/preview/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\PageController::preview/$1"}, {"method": "GET", "route": "admin/menus", "handler": "\\App\\Controllers\\Admin\\MenuController::index"}, {"method": "GET", "route": "admin/menus/create", "handler": "\\App\\Controllers\\Admin\\MenuController::create"}, {"method": "GET", "route": "admin/menus/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\MenuController::edit/$1"}, {"method": "GET", "route": "admin/menus/builder/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\MenuController::builder/$1"}, {"method": "GET", "route": "admin/menu-items/get/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\MenuItemController::get/$1"}, {"method": "GET", "route": "admin/menu-items/menu/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\MenuItemController::getMenuItems/$1"}, {"method": "GET", "route": "admin/menu-items/search-pages", "handler": "\\App\\Controllers\\Admin\\MenuItemController::searchPages"}, {"method": "GET", "route": "admin/blog", "handler": "\\App\\Controllers\\Admin\\BlogController::index"}, {"method": "GET", "route": "admin/blog/create", "handler": "\\App\\Controllers\\Admin\\BlogController::create"}, {"method": "GET", "route": "admin/blog/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\BlogController::edit/$1"}, {"method": "GET", "route": "admin/blog/search", "handler": "\\App\\Controllers\\Admin\\BlogController::search"}, {"method": "GET", "route": "admin/blog/by-status", "handler": "\\App\\Controllers\\Admin\\BlogController::getPostsByStatus"}, {"method": "GET", "route": "admin/categories", "handler": "\\App\\Controllers\\Admin\\CategoryController::index"}, {"method": "GET", "route": "admin/categories/create", "handler": "\\App\\Controllers\\Admin\\CategoryController::create"}, {"method": "GET", "route": "admin/categories/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\CategoryController::edit/$1"}, {"method": "GET", "route": "admin/tags", "handler": "\\App\\Controllers\\Admin\\TagController::index"}, {"method": "GET", "route": "admin/tags/create", "handler": "\\App\\Controllers\\Admin\\TagController::create"}, {"method": "GET", "route": "admin/tags/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\TagController::edit/$1"}, {"method": "GET", "route": "admin/tags/search", "handler": "\\App\\Controllers\\Admin\\TagController::search"}, {"method": "GET", "route": "admin/comments", "handler": "\\App\\Controllers\\Admin\\CommentController::index"}, {"method": "GET", "route": "admin/comments/pending", "handler": "\\App\\Controllers\\Admin\\CommentController::pending"}, {"method": "GET", "route": "admin/media", "handler": "\\App\\Controllers\\Admin\\MediaController::index"}, {"method": "GET", "route": "admin/media/get-media", "handler": "\\App\\Controllers\\Admin\\MediaController::getMedia"}, {"method": "GET", "route": "admin/media/details/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\MediaController::details/$1"}, {"method": "GET", "route": "admin/media/picker", "handler": "\\App\\Controllers\\Admin\\MediaController::picker"}, {"method": "GET", "route": "admin/media/stats", "handler": "\\App\\Controllers\\Admin\\MediaController::stats"}, {"method": "GET", "route": "admin/themes", "handler": "\\App\\Controllers\\Admin\\ThemeController::index"}, {"method": "GET", "route": "admin/themes/create", "handler": "\\App\\Controllers\\Admin\\ThemeController::create"}, {"method": "GET", "route": "admin/themes/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\ThemeController::edit/$1"}, {"method": "GET", "route": "admin/themes/settings", "handler": "\\App\\Controllers\\Admin\\ThemeController::settings"}, {"method": "GET", "route": "admin/users", "handler": "\\App\\Controllers\\Admin\\UserController::index"}, {"method": "GET", "route": "admin/users/create", "handler": "\\App\\Controllers\\Admin\\UserController::create"}, {"method": "GET", "route": "admin/users/edit/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\UserController::edit/$1"}, {"method": "GET", "route": "admin/users/profile/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\UserController::profile/$1"}, {"method": "POST", "route": "blog/comment", "handler": "\\App\\Controllers\\BlogController::submitComment"}, {"method": "POST", "route": "auth/login", "handler": "\\App\\Controllers\\AuthController::processLogin"}, {"method": "POST", "route": "auth/register", "handler": "\\App\\Controllers\\AuthController::processRegister"}, {"method": "POST", "route": "user/profile", "handler": "\\App\\Controllers\\UserDashboardController::updateProfile"}, {"method": "POST", "route": "admin/pages/store", "handler": "\\App\\Controllers\\Admin\\PageController::store"}, {"method": "POST", "route": "admin/pages/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\PageController::update/$1"}, {"method": "POST", "route": "admin/menus/store", "handler": "\\App\\Controllers\\Admin\\MenuController::store"}, {"method": "POST", "route": "admin/menus/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\MenuController::update/$1"}, {"method": "POST", "route": "admin/menus/duplicate/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\MenuController::duplicate/$1"}, {"method": "POST", "route": "admin/menu-items/store", "handler": "\\App\\Controllers\\Admin\\MenuItemController::store"}, {"method": "POST", "route": "admin/menu-items/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\MenuItemController::update/$1"}, {"method": "POST", "route": "admin/menu-items/update-order", "handler": "\\App\\Controllers\\Admin\\MenuItemController::updateOrder"}, {"method": "POST", "route": "admin/menu-items/bulk-status", "handler": "\\App\\Controllers\\Admin\\MenuItemController::bulkUpdateStatus"}, {"method": "POST", "route": "admin/blog/store", "handler": "\\App\\Controllers\\Admin\\BlogController::store"}, {"method": "POST", "route": "admin/blog/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\BlogController::update/$1"}, {"method": "POST", "route": "admin/blog/duplicate/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\BlogController::duplicate/$1"}, {"method": "POST", "route": "admin/blog/bulk-action", "handler": "\\App\\Controllers\\Admin\\BlogController::bulkAction"}, {"method": "POST", "route": "admin/categories/store", "handler": "\\App\\Controllers\\Admin\\CategoryController::store"}, {"method": "POST", "route": "admin/categories/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\CategoryController::update/$1"}, {"method": "POST", "route": "admin/categories/reorder", "handler": "\\App\\Controllers\\Admin\\CategoryController::reorder"}, {"method": "POST", "route": "admin/tags/store", "handler": "\\App\\Controllers\\Admin\\TagController::store"}, {"method": "POST", "route": "admin/tags/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\TagController::update/$1"}, {"method": "POST", "route": "admin/comments/approve/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\CommentController::approve/$1"}, {"method": "POST", "route": "admin/comments/spam/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\CommentController::spam/$1"}, {"method": "POST", "route": "admin/comments/trash/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\CommentController::trash/$1"}, {"method": "POST", "route": "admin/comments/bulk-action", "handler": "\\App\\Controllers\\Admin\\CommentController::bulkAction"}, {"method": "POST", "route": "admin/media/upload", "handler": "\\App\\Controllers\\Admin\\MediaController::upload"}, {"method": "POST", "route": "admin/media/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\MediaController::update/$1"}, {"method": "POST", "route": "admin/media/bulk-delete", "handler": "\\App\\Controllers\\Admin\\MediaController::bulkDelete"}, {"method": "POST", "route": "admin/media/generate-thumbnails", "handler": "\\App\\Controllers\\Admin\\MediaController::generateThumbnails"}, {"method": "POST", "route": "admin/media/cleanup", "handler": "\\App\\Controllers\\Admin\\MediaController::cleanup"}, {"method": "POST", "route": "admin/themes/store", "handler": "\\App\\Controllers\\Admin\\ThemeController::store"}, {"method": "POST", "route": "admin/themes/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\ThemeController::update/$1"}, {"method": "POST", "route": "admin/themes/set-default/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\ThemeController::setDefault/$1"}, {"method": "POST", "route": "admin/themes/settings", "handler": "\\App\\Controllers\\Admin\\ThemeController::updateSettings"}, {"method": "POST", "route": "admin/users/store", "handler": "\\App\\Controllers\\Admin\\UserController::store"}, {"method": "POST", "route": "admin/users/update/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\UserController::update/$1"}, {"method": "POST", "route": "admin/users/toggle-status/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\UserController::toggleStatus/$1"}, {"method": "POST", "route": "admin/users/bulk-action", "handler": "\\App\\Controllers\\Admin\\UserController::bulkAction"}, {"method": "DELETE", "route": "admin/pages/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\PageController::delete/$1"}, {"method": "DELETE", "route": "admin/menus/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\MenuController::delete/$1"}, {"method": "DELETE", "route": "admin/menu-items/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\MenuItemController::delete/$1"}, {"method": "DELETE", "route": "admin/blog/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\BlogController::delete/$1"}, {"method": "DELETE", "route": "admin/categories/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\CategoryController::delete/$1"}, {"method": "DELETE", "route": "admin/tags/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\TagController::delete/$1"}, {"method": "DELETE", "route": "admin/comments/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\CommentController::delete/$1"}, {"method": "DELETE", "route": "admin/media/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\MediaController::delete/$1"}, {"method": "DELETE", "route": "admin/themes/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\ThemeController::delete/$1"}, {"method": "DELETE", "route": "admin/users/delete/([0-9]+)", "handler": "\\App\\Controllers\\Admin\\UserController::delete/$1"}]}, "badgeValue": 64, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "14.81", "count": 1}, "DBQuery": {"event": "DB<PERSON>uery", "duration": "0.06", "count": 1}}}, "badgeValue": 2, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.619369, "duration": 0.014807939529418945}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.684306, "duration": 5.602836608886719e-05}]}], "vars": {"varData": {"View Data": {"title": "Create Template", "user": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (6)</dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>username</dfn> =&gt; <var>string</var> (5) \"admin\"<div class=\"access-path\">$value['username']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (15) \"<EMAIL>\"<div class=\"access-path\">$value['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>role</dfn> =&gt; <var>string</var> (5) \"admin\"<div class=\"access-path\">$value['role']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>first_name</dfn> =&gt; <var>string</var> (5) \"Admin\"<div class=\"access-path\">$value['first_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_name</dfn> =&gt; <var>string</var> (4) \"User\"<div class=\"access-path\">$value['last_name']</div></dt></dl></dd></dl></div>"}}, "session": {"__ci_last_regenerate": "<pre>**********</pre>", "_ci_previous_url": "http://localhost:8081/admin/themes/create", "user_id": "1", "username": "admin", "email": "<EMAIL>", "role": "admin", "first_name": "Admin", "last_name": "User", "is_logged_in": "<pre>1</pre>", "__ci_vars": "<pre>Array\n(\n)\n</pre>"}, "headers": {"Host": "localhost:8081", "Connection": "keep-alive", "Sec-Ch-Ua": "&quot;Brave&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;", "Sec-Ch-Ua-Mobile": "?0", "Sec-Ch-Ua-Platform": "&quot;Linux&quot;", "Upgrade-Insecure-Requests": "1", "User-Agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8", "Sec-Gpc": "1", "Accept-Language": "en-US,en;q=0.8", "Sec-Fetch-Site": "none", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-User": "?1", "Sec-Fetch-Dest": "document", "Accept-Encoding": "gzip, deflate, br, zstd", "Cookie": "admin_auth=eyJpdiI6IkQ3T3RUQngyc043Q3RZSWVnSjhWaFE9PSIsInZhbHVlIjoiRTlGaDVKSTMxdm15ODBTM2tvZFdQRldxL3RNNnY4UXJBc3RDWGU3SUJMVFhKYnJ1OTM4NzZLb0FWVzBRTDdZTy9abzg1MXJuWTNBaW0wNElrMDd2NUtheFNvTlI4cWc4NlhVa3VoaXBHOTJJRDgybGQ2WXp1NVkxODBFVTliVzYyQ2Q5bEo4bVNrbjhDaVBmbDNNNE1BPT0iLCJtYWMiOiJmNzIxZjQzOWQ0ZDJiOGUxNTU3ODZlZmI3YzllYmI3MGRhNjQ3YWIxZTc2NGUyNTMzOTg4NjA1YmIyZGRiYzAwIiwidGFnIjoiIn0%3D; ci_session=03ea20c9daaf1d2d5d5e1911e9210a30"}, "cookies": {"admin_auth": "eyJpdiI6IkQ3T3RUQngyc043Q3RZSWVnSjhWaFE9PSIsInZhbHVlIjoiRTlGaDVKSTMxdm15ODBTM2tvZFdQRldxL3RNNnY4UXJBc3RDWGU3SUJMVFhKYnJ1OTM4NzZLb0FWVzBRTDdZTy9abzg1MXJuWTNBaW0wNElrMDd2NUtheFNvTlI4cWc4NlhVa3VoaXBHOTJJRDgybGQ2WXp1NVkxODBFVTliVzYyQ2Q5bEo4bVNrbjhDaVBmbDNNNE1BPT0iLCJtYWMiOiJmNzIxZjQzOWQ0ZDJiOGUxNTU3ODZlZmI3YzllYmI3MGRhNjQ3YWIxZTc2NGUyNTMzOTg4NjA1YmIyZGRiYzAwIiwidGFnIjoiIn0=", "ci_session": "03ea20c9daaf1d2d5d5e1911e9210a30"}, "request": "HTTP/1.1", "response": {"statusCode": 200, "reason": "OK", "contentType": "text/html; charset=UTF-8", "headers": {"Content-Type": "text/html; charset=UTF-8"}}}, "config": {"ciVersion": "4.6.1", "phpVersion": "8.2.12", "phpSAPI": "cli-server", "environment": "development", "baseURL": "http://localhost:8081/", "timezone": "UTC", "locale": "en", "cspEnabled": false}}